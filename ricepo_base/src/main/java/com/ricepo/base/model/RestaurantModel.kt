package com.ricepo.base.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.ricepo.base.parser.Exclude
import com.ricepo.base.parser.ParserFacade
import kotlinx.parcelize.Parcelize

/**
 * remote object entity with network level
 * all default value generate empty construct
 */
@Parcelize
data class Restaurant(
  @SerializedName("_id")
  var id: String? = null,
  val canPickup: Boolean? = null,
  val color: String? = null,
  val hours: List<Hour>? = null,
  val language: String? = null,
  val location: RestaurantLocation? = null,
  val tags: List<String>? = null,
  val tax: Double? = null,
  val region: RestaurantRegion? = null,
  val fake: Boolean = false,
  val createdAt: String? = null,
  val name: InternationalizationContent? = null,
  //    let acceptCredit: Bool?
  val acceptCredit: String? = null,
  val delivery: RestaurantDelivery? = null,
  val updatedAt: String? = null,
  val manual: Boolean? = null,
  val zscore: Double? = null,
  var score: RestaurantScore? = null,
  val timezone: String? = null,
  val credit: Credit? = null,
  val featured: Boolean? = false,
  val suspended: Boolean? = false,
  val canDeliver: Boolean? = false,
  @Exclude
  var address: Address? = null,
//    var requireTip: @RawValue Any? = null,
  @Exclude
  var requireTip: RequireTip? = null,
  var closed: Closed? = null,
  val items: List<Food>? = null,
  var isClosed: String? = null,
  val phone: String? = null,
  var groups: List<RestaurantGroup>? = null,
  var group: RestaurantGroup? = null,
  val promotion: InternationalizationContent? = null,
  val vipPromotion: InternationalizationContent? = null,
  val motd: InternationalizationContent? = null,
  val feature: InternationalizationContent? = null,
  // Search match info
  var match: RestaurantSearchMatch? = null,
  /**
   * Added for supermarket errand business
   * Whether the page is fixed to light mode and the background color is fixed to white
   */
  val light: Boolean? = true,
  // / Indicate with subscription promotion
  val vip: Boolean? = false,
  // Reward
  val reward: Reward? = null,
  // service fee
  val serviceFee: Discount? = null,
  val lastOrder: LastOrder? = null,
  val bundleItems: List<Food>? = null,
  val hourlyClosed: Boolean? = null,
  val image: ThemeImage? = null,
  val distance: Float? = null,
  var pool: RestaurantPool? = null,
  var billboard: List<RestaurantInfo>? = null,
  val images: Images? = null,
  val customerRating: CustomerRating? = null,
  val useLargeImage: Boolean? = false
) : Parcelable {

  fun deepCopy(): Restaurant {
    val gson = ParserFacade.buildGson()
    val json = gson.toJson(this)
    return gson.fromJson(json, object : TypeToken<Restaurant>() {}.type)
  }

  companion object {
    /**
     * check if it is a supermarket
     */
    fun isMarketCategory(restaurant: Restaurant?): Boolean {
      return restaurant?.tags?.isNotEmpty() == true && restaurant?.tags?.get(0) == "market"
    }
  }
}

@Parcelize
class Images(val landing: String?) : Parcelable

// customerRating:{ score:4.5, count: 23 }
@Parcelize
class CustomerRating(
  val score: Double?,
  val count: Int?,
  val message: String?,
) : Parcelable

@Parcelize
data class RestaurantInfo(
  val type: String? = null,
  val content: InternationalizationContent? = null
) : Parcelable {
  companion object {
    const val TYPE_PROMOTION = "promotion"
    const val TYPE_VIP = "vip"
    const val TYPE_INFO = "info"
    const val TYPE_TIME = "time"
    const val TYPE_FEATURE = "feature"
  }
}

@Parcelize
data class RestaurantPool(
  @SerializedName("_id")
  val id: String? = null,
  val expiresAt: String? = null,
  val discount: Int? = null,
  val location: RestaurantLocation? = null,
  val message: InternationalizationContent? = null,
  val expired: Boolean? = null,
) : Parcelable

// last order from restaurant
@Parcelize
data class LastOrder(
  val time: InternationalizationContent? = null,
  val items: InternationalizationContent? = null
) : Parcelable

/**
 * Rules for compare scores
 * fake > manually closed > hourly closed > search match > featured > minimum
 * tuple cant compare when size more than 7
 * [fake, closed, isClosed, id, matchName, matchTags, matchFood, featured, minimun, zscore]
 */
@Parcelize
class RestaurantScore : Parcelable {

    /*
     * 1: not fake
     * 0: fake
     */
  var fake: Double = 1.0
    /*
     * 1: not closed
     * 0: closed
     */
  var closed: Double = 1.0

    /*
     * 1: not isClosed
     * 0: isClosed
     */
  @SerializedName("isClosed")
  var isClosedVal: Double = 1.0

  var id: Double = 0.0
  var matchName: Double = 0.0
  var matchTags: Double = 0.0
  var matchFood: Double = 0.0
  var featured: Double = 0.0

    /*
     * 2: minimum is 0
     * (1 / minimum): less minimum on the top
     * 0: has no fees
     */
  var minimun: Double = 0.0

  // Balance is actually an Int type, but it's used here for sorting, so it's simply changed to a Double type.
  var balance: Double = 0.0

  override fun toString(): String {
    return listOf<Any>(
      fake, closed, isClosedVal, id, matchName, matchTags, matchFood,
      featured, minimun, balance
    ).toString()
  }
}

@Parcelize
data class RestaurantSearchMatch(
  val name: Boolean?,
  val tags: List<String>?,
  val food: List<Food>?
) : Parcelable

@Parcelize
data class RestaurantGroup(
  val name: InternationalizationContent? = null,
  val index: Int? = null,
  var limit: Int? = null,
  val image: ThemeImage? = null,
  val description: InternationalizationContent? = null,
  val groupId: String? = null,
  val type: String? = null,
  val jump: Boolean? = null,
  var jumpData: CategoryJumpData? = null,
  var titleStyle: String? = null
) : Parcelable

data class RestaurantRemoteSection(
  val sections: List<RestaurantRemoteGroup>? = null,
  val tabs: List<RestaurantTab>? = null
)

data class RestaurantTab(
  val name: InternationalizationContent? = null,
  val type: String? = null,
  val selected: Boolean? = null,
  val attention: Boolean? = null
) {
  companion object {
    const val TYPE_ONDEMAND = "ondemand"
    const val TYPE_SCHEDULED = "scheduled"
    const val TYPE_PICKUP = "pickup"
  }
}

data class RestaurantRemoteGroup(
  var restaurants: List<Restaurant>? = null,
  val name: InternationalizationContent? = null,
  val description: InternationalizationContent? = null,
  var type: String? = null,
  val image: ThemeImage? = null,
  val index: Double? = null,
  val data: RestaurantGroupData? = null,
  var cursor: String? = null,
  val groupId: String? = null,
  val hasNextPage: Boolean? = null,
  // indicator the vertical group if has more data
  var hasMore: Boolean? = null,
  val jump: Boolean? = null,
  var sorts: List<RestaurantSort>? = null,
  var background: ThemeImage? = null,
  var titleStyle: String? = null,
) {
  companion object {
    val TITLE_STYLE_PROMOTION = "promotion"
    val TITLE_STYLE_VIP = "vip"
  }
}

@Parcelize
data class RestaurantSort(
  @SerializedName("_id")
  var id: String? = null,
  var name: InternationalizationContent? = null,
  var order: Int? = null,
  var selected: Boolean = false,
  // the group item position
  var position: Int? = null,
) : Parcelable

object RestaurantGroupType {
  const val vertical = "vertical"
  const val horizontal = "horizontal"
  const val plan = "plan"
  const val cuisine = "cuisine"
  const val recommend = "recommend"
  const val lucky = "lucky"
  const val dishFood = "food"
  const val lucky_v2 = "lucky-v2"
  const val motd = "motd"
  const val banner = "banners"
  const val cascade = "cascade"
}

data class RestaurantGroupData(
  val plan: SubscriptionPlan? = null,
  val cuisines: List<Cuisine>? = null,
  // section recommend
  val recommend: RestaurantRecommendData? = null,
  val top: RestaurantRecommendData? = null,
  // section lucky
  val name: InternationalizationContent? = null,
  val button: InternationalizationContent? = null,
  val items: List<RestaurantDishItem>? = null,
  val restaurants: List<LuckRecommendBean>? = null,
  val banners: List<Banner>? = null,
  // motd message
  val motd: InternationalizationContent? = null,
)

@Parcelize
data class Cuisine(
  val name: InternationalizationContent? = null,
  val tag: String? = null,
  val image: ThemeImage? = null
) : Parcelable {
  companion object {
    const val TAG_LUCKY = "lucky"
    const val SHOW_ALL = "SHOW_ALL"
  }
}

data class RestaurantRecommendData(
  val name: InternationalizationContent? = null,
  val images: List<String?>? = null
)

data class RestaurantDishItem(
  @SerializedName("_id")
  val id: String? = null,
  val name: InternationalizationContent? = null,
  val price: Int? = null,
  val originalPrice: Int? = null,
  val restaurant: Restaurant? = null,
  val image: FoodImage? = null,
  val background: ThemeImage? = null,
)

@Parcelize
data class Closed(
  @SerializedName("en-US")
  override val enUS: String?,
  @SerializedName("zh-CN")
  override val zhCN: String?,
  @SerializedName("zh-HK")
  override val zhHK: String?,
  @SerializedName("es")
  override val es: String?,
  val reason: String?,
  val user: String?
) : I18n, Parcelable

/**
 * address sometime a string, sometime an object
 */
sealed class Address : Parcelable {

  var innerItemValue: AddressObj? = null
    get() {
      if (this is AddressObj) {
        return this
      }
      return null
    }
}

@Parcelize
class AddressStr(val v1: String) : Address()

@Parcelize
data class AddressObj(
  val number: String?,
  val street: String?,
  val city: String?,
  val country: String?,
  val zipcode: String?,
  val location: RestaurantLocation?,
  val unit: String?,
  val formatted: String,
  val state: String?
) : Address()

@Parcelize
data class RestaurantLocation(
  val type: String,
  val coordinates: List<Double>
) : Parcelable

@Parcelize
data class Credit(
  val minimum: Double,
  val fee: Fee
) : Parcelable

@Parcelize
data class RestaurantDelivery(
//    @Exclude
//    var cutoff: Cutoff? = null,
  val estimate: Estimate? = null,
  val fee: Fee? = null,
  val fees: List<DeliveryZoneFees?>? = null,
//    val geometry: RegionGeometry? = null,
//    val locationId: String? = null,
  val minimum: Int? = null,
  val provider: String? = null,
//    val type: String? = null,
  val busy: Boolean? = null,
  val scheduled: Boolean? = null,
) : Parcelable

@Parcelize
data class Courier(
  @SerializedName("_id")
  val id: String?,
  val email: String?,
  val image: String?,
  val name: String?,
  val phone: String?
) : Parcelable

sealed class Cutoff : Parcelable {
  @Parcelize
  data class double(val v1: Double) : Cutoff(), Parcelable
  @Parcelize
  data class innerItem(val v1: CutoffObj) : Cutoff(), Parcelable
}

@Parcelize
data class CutoffObj(
  val cutoff: Double?,
  val provider: String?
) : Parcelable

// MARK: - Hour
@Parcelize
data class Hour(
  val start: Int,
  val end: Int,
  val dayOfWeek: Int,
  val type: String?
) : Parcelable

// MARK: - Preferences
data class Preferences(val dailyReport: Boolean, val autoConfirm: Boolean, val driverCopy: Boolean)

// MARK: - Region
@Parcelize
data class RestaurantRegion(
  @SerializedName("_id")
  val id: String
) : Parcelable

// Require Tip
sealed class RequireTip : Parcelable {
  @Parcelize
  data class double(val v1: Double) : RequireTip()

  @Parcelize
  data class bool(val v1: Boolean) : RequireTip()

  fun doubleValue(): Double? {
    return when (this) {
      is double -> this.v1
      else -> null
    }
  }
}

@Parcelize
data class Reward(
  var enabled: Boolean?,
  var balance: Int?,
  val amount: Int?
) : Parcelable

/*
 * Restuarnts colleciton
 * delivery.zone.features.properties.fees
 */
@Parcelize
data class DeliveryZoneFees(
  val adjustments: Adjustments?,
  val delivery: Int,
  val minimum: Double
) : Parcelable

@Parcelize
data class Adjustments(
  val ricepo: Int?,
  val driver: Int?,
  val customer: Int?,
  val restaurant: Int?,
  val reason: String?
) : Parcelable

@Parcelize
data class RegionGeometry(
  val type: String
//    val coordinates: RegionGeometryCoordinates
) : Parcelable

@Parcelize
data class Discount(
  val flat: Int?,
  val factor: Double?
) : Parcelable

@Parcelize
data class LuckRecommendBean(
  val items: List<Food>,
  val restaurant: Restaurant,
  // index for firebase event
  var index: Int? = null
) : Parcelable

@Parcelize
data class RestaurantSearchResult(
  val restaurants: List<Restaurant>? = null,
  val recommend: List<Restaurant>? = null,
) : Parcelable
