package com.ricepo.network

object EnvNetwork {

  /**
   * develop environment
   */
  const val DEV_URL = "https://dev.isengard.rice.rocks/"

  /**
   * online environment
   */
  const val REL_URL = "https://vanguard.rice.rocks/"

  /**
   * ricepo terms link
   */
  const val RICEPO_URL_TERMS = "https://rice.rocks/terms.html"

  /**
   * ricepo policy link
   */
  const val RICEPO_URL_PRIVACY = "https://rice.rocks/privacy.html"

  /**
   * support chat url
   */
  const val CHAT_URL = "https://chat.rice.rocks"

  /**
   * order receipt url
   */
  const val RECEIPT_URL = "https://s.rice.rocks/o/"

  /**
   * ricepo web site
   */
  const val RICEPO_WEBSITE = "https://www.rice.rocks/"

  /**
   * refer url xiaohongshu
   */
  const val REFER_URL = "https://ricepo-red.s3.amazonaws.com/instruction.html"
}
