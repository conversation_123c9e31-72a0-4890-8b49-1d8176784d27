package com.ricepo.network.resource

//
// Created by <PERSON><PERSON> on 2/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class NetworkException(
  message: String,
  cause: Throwable
) : Exception(message, cause)

/**
 * get the lowest level throwable
 *  maybe not real throwable
 */
fun Throwable?.getCause(): Throwable? {
  var c = this
  var r = this
  while (c != null) {
    if (c.cause == null) {
      r = c
    }
    c = c.cause
  }
  return r
}
