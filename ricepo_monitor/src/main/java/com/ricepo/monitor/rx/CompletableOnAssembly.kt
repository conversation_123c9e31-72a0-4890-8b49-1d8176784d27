/*
 * Copyright 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ricepo.monitor.rx

import io.reactivex.rxjava3.core.Completable
import io.reactivex.rxjava3.core.CompletableObserver
import io.reactivex.rxjava3.core.CompletableSource
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.internal.disposables.DisposableHelper

/**
 * Wraps a CompletableSource and inject the assembly info.
 */
internal class CompletableOnAssembly(val source: CompletableSource) :
  Completable() {
  val assembled: RxJavaAssemblyException
  override fun subscribeActual(observer: CompletableObserver) {
    source.subscribe(OnAssemblyCompletableObserver(observer, assembled))
  }

  internal class OnAssemblyCompletableObserver(
    val downstream: CompletableObserver,
    val assembled: RxJavaAssemblyException
  ) : CompletableObserver, Disposable {
    var upstream: Disposable? = null
    override fun onSubscribe(d: Disposable) {
      if (DisposableHelper.validate(upstream, d)) {
        upstream = d
        downstream.onSubscribe(this)
      }
    }

    override fun onError(t: Throwable) {
      assembled.appendLast(t)?.let { downstream.onError(it) }
    }

    override fun onComplete() {
      downstream.onComplete()
    }

    override fun isDisposed(): Boolean {
      return upstream!!.isDisposed
    }

    override fun dispose() {
      // don't break the link here
      upstream!!.dispose()
    }
  }

  init {
    assembled = RxJavaAssemblyException()
  }
}
