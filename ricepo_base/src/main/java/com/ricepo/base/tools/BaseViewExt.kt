package com.ricepo.base.tools

import android.app.Activity
import android.content.ContextWrapper
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.ricepo.base.ErrorInput
import com.ricepo.base.R
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.style.ResourcesUtil

fun View.getActivity(): Activity {
  var context = context
  while (context is ContextWrapper) {
    if (context is Activity) {
      return context
    }
    context = context.baseContext
  }
  return context as Activity
}

fun ViewGroup.showErrorView(
  errorInput: ErrorInput?
) {
  val layoutInflater = getActivity().layoutInflater
  if (errorInput != null) {
    val errorView = layoutInflater.inflate(
      R.layout.layout_error_container,
      null
    )
    addView(errorView)

    errorView.apply {
      this.findViewById<ImageView>(R.id.iv_error_icon)?.setImageResource(errorInput.imageId)
      this.findViewById<TextView>(R.id.tv_error_title)?.text = ResourcesUtil.getString(errorInput.titleId)
      if (errorInput.message != null) {
        this.findViewById<TextView>(R.id.tv_error_message)?.text = errorInput.message
      }
      this.findViewById<TextView>(R.id.btn_error_operator)?.text = ResourcesUtil.getString(errorInput.buttonId)
      this.findViewById<TextView>(R.id.btn_error_operator)?.clickWithTrigger {
        if (errorInput.isCleanWhenClick) {
          removeAllViews()
        }
        errorInput.click()
      }
    }
  }
}
