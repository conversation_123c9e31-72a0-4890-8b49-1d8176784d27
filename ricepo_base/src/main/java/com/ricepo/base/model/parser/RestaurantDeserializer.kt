package com.ricepo.base.model.parser

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.AddressStr
import com.ricepo.base.model.Food
import com.ricepo.base.model.RequireTip
import com.ricepo.base.model.Restaurant
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 30/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantDeserializer : JsonDeserializer<Restaurant> {
  override fun deserialize(
    json: JsonElement?,
    typeOfT: Type?,
    context: JsonDeserializationContext?
  ): Restaurant {

    json as JsonObject

    // don't use ParserFacade.gsonBuilder and buildGson()
    val gson = ParserFacade().builder()
      .registerTypeAdapter(Food::class.java, FoodDeserializer())
      .create()

    val restaurant = gson.fromJson(json, Restaurant::class.java)

    var addressElement = json.get("address")

    if (addressElement != null) {
      if (addressElement.isJsonObject) {
        val addressObj = gson.fromJson(addressElement, AddressObj::class.java)
        restaurant.address = addressObj
      } else {
        restaurant.address = AddressStr(addressElement.toString())
      }
    }

    var requireTipElement = json.get("requireTip")

    // judge require tip multiple type
    requireTipElement?.let {
      if (it.toString() == "false" || it.toString() == "true") {
        restaurant.requireTip = RequireTip.bool(it.asBoolean)
      } else {
        try {
          restaurant.requireTip = if (it.isJsonNull) null else RequireTip.double(it.asDouble)
        } catch (ee: ClassCastException) {
          ee.printStackTrace()
        }
      }
    }

    return restaurant
  }
}
