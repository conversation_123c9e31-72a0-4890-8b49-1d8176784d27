package com.ricepo.base

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.base.R

open class BaseFragment : Fragment() {

  lateinit var firebaseAnalytics: FirebaseAnalytics

  var isVisibleToUser: Boolean = true

  override fun onAttach(context: Context) {
    super.onAttach(context)
    DisplayUtil.setDensity(context.resources)
    firebaseAnalytics = Firebase.analytics
  }

  override fun onHiddenChanged(hidden: Boolean) {
    super.onHiddenChanged(hidden)
    isVisibleToUser = !hidden
  }

  /**
   * show error view
   */
  protected fun showErrorView(
    viewGroup: ViewGroup,
    iconId: Int,
    titleResId: Int,
    message: String?,
    btnResId: Int,
    isRemoveView: Boolean = true,
    listener: View.OnClickListener? = null
  ) {
    val errorView = layoutInflater.inflate(R.layout.layout_error_container, null)
    viewGroup.addView(errorView)
    errorView.apply {
      this.findViewById<ImageView>(R.id.iv_error_icon)?.setImageResource(iconId)
      this.findViewById<TextView>(R.id.tv_error_title)?.text = ResourcesUtil.getString(titleResId)
      if (message != null) this.findViewById<TextView>(R.id.tv_error_message)?.text = message
      this.findViewById<TextView>(R.id.btn_error_operator)?.text = ResourcesUtil.getString(btnResId)
      this.findViewById<TextView>(R.id.btn_error_operator)?.clickWithTrigger {
        if (isRemoveView) {
          viewGroup.removeAllViews()
        }
        listener?.onClick(this)
      }
    }
  }

  /**
   * remove the error view
   */
  protected fun clearErrorView(viewGroup: ViewGroup) {
    viewGroup.removeAllViews()
  }
}
