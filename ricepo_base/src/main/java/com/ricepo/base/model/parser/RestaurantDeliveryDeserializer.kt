package com.ricepo.base.model.parser

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.ricepo.base.model.RestaurantDelivery
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 17/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantDeliveryDeserializer : JsonDeserializer<RestaurantDelivery> {
  override fun deserialize(
    json: JsonElement?,
    typeOfT: Type?,
    context: JsonDeserializationContext?
  ): RestaurantDelivery {

    json as JsonObject

    val gson = ParserFacade.gson

    val restaurantDelivery = gson.fromJson(json, RestaurantDelivery::class.java)

//        var cutoffElement = json.get("cutoff")
//
//        if (cutoffElement != null) {
//            if (cutoffElement.isJsonObject) {
//                val cutoffObj = gson.fromJson(cutoffElement, CutoffObj::class.java)
//                restaurantDelivery.cutoff = Cutoff.innerItem(cutoffObj)
//            } else {
//                try {
//                    restaurantDelivery.cutoff = Cutoff.double(cutoffElement.asDouble)
//                } catch (e: Exception) {
//                    e.printStackTrace()
//                }
//            }
//        }

    return restaurantDelivery
  }
}
