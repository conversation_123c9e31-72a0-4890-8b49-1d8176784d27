package com.ricepo.network.executor

import io.reactivex.rxjava3.core.Scheduler

interface PostExecutionThread {

  /**
   * android schedulers main thread
   */
  val mainScheduler: Scheduler

  /**
   * schedulers io
   */
  val ioScheduler: Scheduler

  /**
   * looper thread with handler
   */
  val loopedIoScheduler: Scheduler

  /**
   * schedulers computation
   */
  val computationScheduler: Scheduler
}
