package com.ricepo.base.image

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.widget.ImageView
import com.bumptech.glide.request.target.CustomViewTarget
import com.bumptech.glide.request.transition.Transition
import com.ricepo.base.R
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 9/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class BackgroundTarget(private val imageView: ImageView) :
  CustomViewTarget<ImageView, Bitmap>(imageView) {

  companion object {
    private var backViewHeight: Int = 0
  }

  override fun onLoadFailed(errorDrawable: Drawable?) {
  }

  override fun onResourceCleared(placeholder: Drawable?) {
    backViewHeight = 0
  }

  override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {

    val viewWidth = imageView.width
    val viewHeight = imageView.height

    val imageWidth = resource.width
    val imageHeight = resource.height

    if (imageHeight > viewHeight) {
      backViewHeight = viewHeight
//            // recycler view scroll viewHeight dynamic
//            if (viewHeight > backViewHeight) backViewHeight = viewHeight
      val ratio = (viewWidth / backViewHeight.toFloat())
      val bitmap = cropBitmap(
        resource,
        viewWidth,
        backViewHeight
      )
      if (bitmap?.isRecycled == true) {
        imageView.setImageDrawable(null)
      } else {
        imageView.setImageBitmap(bitmap)
      }
      return
    }

    if (resource.isRecycled) {
      imageView.setImageDrawable(null)
    } else {
      imageView.setImageBitmap(resource)
    }
  }

  fun cropBitmap(bitmap: Bitmap, ratio: Float): Bitmap? {
    val bitmapRatio = (
      bitmap.width.toFloat() /
        bitmap.height.toFloat()
      )
    return if (bitmapRatio != ratio) {
      val finalHeight = bitmap.width / ratio
      cropBitmap(
        bitmap,
        bitmap.width,
        finalHeight.toInt()
      )
    } else {
      bitmap
    }
  }

  private fun cropBitmap(bitmap: Bitmap?, width: Int, height: Int): Bitmap? {
    return try {
      if (bitmap != null) {
        val yOfPlate = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_200dp)
          .times(1).div(8)
//                val yOfPlate = 0
        Bitmap.createBitmap(
          bitmap, 0, yOfPlate, width, height
        )
      } else {
        bitmap
      }
    } catch (ex: Exception) {
      ex.printStackTrace()
      bitmap
    }
  }

//    private fun decodeScaledBitmapWithTargetSampleSize(image: Uri): Bitmap {
//        val header = ImageDecoder.OnHeaderDecodedListener { decoder, info, _ ->
//            val size = info.size
//            val sampleSize = calculateSampleSize(size.width, min(DisplayUtil.getScreenWidth(), size.width))
//            val newSize = Size(size.width / sampleSize, size.height / sampleSize)
//            decoder.setTargetSampleSize(sampleSize)
//            decoder.crop = Rect(0, 0, newSize.width, newSize.height / 2)
//        }
//        val source = ImageDecoder.createSource(contentResolver, image)
//        return ImageDecoder.decodeBitmap(source, header)
//    }

  private fun calculateSampleSize(currentWidth: Int, requiredWidth: Int): Int {
    var inSampleSize = 1
    if (currentWidth > requiredWidth) {
      val halfWidth = currentWidth / 2
      // Calculate the largest inSampleSize value that is a power of 2 and keeps
      // width larger than the requested width
      while (halfWidth / inSampleSize >= requiredWidth) {
        inSampleSize *= 2
      }
    }
    return inSampleSize
  }
}
