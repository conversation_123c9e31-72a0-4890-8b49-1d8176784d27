package com.ricepo.base.extension

import android.os.Looper
import androidx.lifecycle.MutableLiveData

//
// Created by <PERSON><PERSON> on 15/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

fun <T : Any> MutableLiveData<T>.update(t: T?) {
  if (this == null || t == null) {
    return
  }
  if (Thread.currentThread() == Looper.getMainLooper().getThread()) {
    this.setValue(t)
  } else {
    this.postValue(t)
  }
}
