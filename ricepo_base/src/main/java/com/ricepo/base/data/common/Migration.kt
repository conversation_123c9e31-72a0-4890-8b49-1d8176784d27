package com.ricepo.base.data.common

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

//
// Created by <PERSON><PERSON> on 25/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object MigrationConst {

  val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
      // add or rename expected
//            database.execSQL("alter table t_value drop created_at")

      database.execSQL(
        "CREATE TABLE `t_value_12` (`updated_at` INTEGER DEFAULT CURRENT_TIMESTAMP," +
          " `f_key` TEXT NOT NULL, `f_value` TEXT, PRIMARY KEY(`f_key`))"
      )
      database.execSQL(
        "insert into t_value_12(f_key, f_value, updated_at) select f_key, f_value, updated_at " +
          "from t_value"
      )
      database.execSQL("drop table t_value")
      database.execSQL("alter table t_value_12 rename to t_value")
    }
  }
}
