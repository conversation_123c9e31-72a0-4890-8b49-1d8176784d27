package com.ricepo.base.extension

import android.app.Activity
import com.ricepo.base.animation.Loading
import kotlinx.coroutines.flow.AbstractFlow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import java.lang.Exception
import kotlin.experimental.ExperimentalTypeInference

@OptIn(ExperimentalTypeInference::class)
public fun <T> flowLoading(
  context: Activity,
  error: (Throwable) -> Unit = { it.printStackTrace() },
  @BuilderInference block: suspend FlowCollector<T>.() -> Unit
): Flow<T> =
  SafeFlowLoading(context, block, error)

private class SafeFlowLoading<T>(
  private val context: Activity,
  private val block: suspend FlowCollector<T>.() -> Unit,
  private val error: (Throwable) -> Unit = {}
) : AbstractFlow<T>() {

  override suspend fun collectSafely(collector: FlowCollector<T>) {

    try {
      Loading.showLoading(context)
      collector.block()
    } catch (e: Exception) {
      error(e)
    } finally {
      Loading.hideLoading()
    }
  }
}
