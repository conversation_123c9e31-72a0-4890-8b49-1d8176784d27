package com.ricepo.network.restapi

import com.chuckerteam.chucker.api.ChuckerInterceptor
import com.google.gson.Gson
import com.ricepo.network.EnvNetwork
import com.ricepo.network.NetworkConfiguration
import com.ricepo.network.NetworkDebugTools
import com.ricepo.network.adapter.Rx3ErrorCallAdapterFactory
import com.ricepo.network.converter.LocalConverterFactory
import com.ricepo.network.interceptor.HttpStatusInterceptor
import com.ricepo.network.interceptor.UrlHeaderInterceptor
import com.skydoves.sandwich.adapters.ApiResponseCallAdapterFactory
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

class RemoteRestApiFactory<T> {

  /**
   * create google maps api
   */
  fun makeGoogleMapsRestApi(
    isDebug: Boolean,
    configuration: NetworkConfiguration,
    tClass: Class<T>,
    gson: Gson
  ): T {
    val okHttpClient = makeOkHttpClient(makeLoggingInterceptor(isDebug), configuration, isDebug)
    val baseUrl = "https://maps.google.com/"

    val retrofit = Retrofit.Builder()
      .baseUrl(baseUrl)
      .client(okHttpClient)
      .addCallAdapterFactory(Rx3ErrorCallAdapterFactory.create())
      .addConverterFactory(LocalConverterFactory.create(gson))
      .build()
    return retrofit.create(tClass)
  }

  /**
   * create google maps api
   */
  fun makeGoogleMapsApi(
    isDebug: Boolean,
    configuration: NetworkConfiguration,
    tClass: Class<T>
  ): T {
    val okHttpClient = makeOkHttpClient(
      makeLoggingInterceptor(isDebug),
      configuration,
      isDebug,
      usePreErrorHandler = false
    )
    val baseUrl = "https://maps.googleapis.com/maps/api/"
    val retrofit = Retrofit.Builder()
      .baseUrl(baseUrl)
      .client(okHttpClient)
      .addConverterFactory(GsonConverterFactory.create())
      .addCallAdapterFactory(ApiResponseCallAdapterFactory.create())
      .build()
    return retrofit.create(tClass)
  }

  /**
   * create remote rest api client
   * @param isDebug The app is debug or release
   * @param tClass The class typeof RestApi
   * @param gson The gson by gson builder create
   */
  fun makeRemoteRestApi(
    isDebug: Boolean,
    configuration: NetworkConfiguration,
    tClass: Class<T>,
    gson: Gson
  ): T {
    val okHttpClient = makeOkHttpClient(makeLoggingInterceptor(isDebug), configuration, isDebug)
    return makeRemoteRestApi(isDebug, okHttpClient, tClass, gson)
  }

  fun makeCoroutineApi(
    isDebug: Boolean,
    configuration: NetworkConfiguration,
    tClass: Class<T>,
  ): T {
    val okHttpClient = makeOkHttpClient(
      makeLoggingInterceptor(isDebug),
      configuration,
      isDebug,
      usePreErrorHandler = false
    )
    var baseUrl = EnvNetwork.REL_URL
    if (isDebug) {
      baseUrl = EnvNetwork.DEV_URL
    }

    val retrofit = Retrofit.Builder()
      .baseUrl(baseUrl)
      .client(okHttpClient)
      .addConverterFactory(GsonConverterFactory.create())
      .addCallAdapterFactory(ApiResponseCallAdapterFactory.create())
      .build()
    return retrofit.create(tClass)
  }

  /**
   * set retrofit configuration
   * @param isDebug The app is debug or release
   * @param okHttpClient The OK Http client
   * @param tClass The class typeof RestApi
   * @param gson The json convert by gson builder create
   */
  private fun makeRemoteRestApi(
    isDebug: Boolean,
    okHttpClient: OkHttpClient,
    tClass: Class<T>,
    gson: Gson
  ): T {
    // The base url is https://example.com/
    var baseUrl = EnvNetwork.REL_URL
    if (isDebug) {
      baseUrl = EnvNetwork.DEV_URL
    }

    val retrofit = Retrofit.Builder()
      .baseUrl(baseUrl)
      .client(okHttpClient)
      .addCallAdapterFactory(Rx3ErrorCallAdapterFactory.create())
//            .addConverterFactory(GsonConverterFactory.create(gson))
      .addConverterFactory(LocalConverterFactory.create(gson))
      .build()
    return retrofit.create(tClass)
  }

  /**
   * set interceptor and timeout
   */
  private fun makeOkHttpClient(
    httpLoggingInterceptor: HttpLoggingInterceptor,
    configuration: NetworkConfiguration,
    isDebug: Boolean,
    usePreErrorHandler: Boolean = true
  ): OkHttpClient {
    val sslContext = SSLContext.getInstance("TLS")
    sslContext.init(null, arrayOf(myX509TrustManager), null)
    return OkHttpClient.Builder().apply {
      interceptors() += listOf(
        httpLoggingInterceptor,
        UrlHeaderInterceptor(configuration),
      )
      if (usePreErrorHandler) {
        addInterceptor(HttpStatusInterceptor())
      }
      NetworkDebugTools.getDebugContext()?.let {
        addInterceptor(ChuckerInterceptor.Builder(context = it).build())
      }
      retryOnConnectionFailure(false)
      connectTimeout(10, TimeUnit.SECONDS)
      readTimeout(if (isDebug) 60 else 30, TimeUnit.SECONDS)
    }.build()
  }

  /**
   * set log if debug mode
   */
  private fun makeLoggingInterceptor(isDebug: Boolean): HttpLoggingInterceptor {
    val logging = HttpLoggingInterceptor()
    logging.level = if (isDebug) {
      HttpLoggingInterceptor.Level.BODY
    } else {
      HttpLoggingInterceptor.Level.NONE
    }
    return logging
  }
}

private val myX509TrustManager: TrustManager = object : X509TrustManager {

  override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {
  }

  override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {
  }

  override fun getAcceptedIssuers(): Array<X509Certificate> {
    // Attempt to get length of null array okhttp3 null throw exception
    return arrayOf()
  }
}
