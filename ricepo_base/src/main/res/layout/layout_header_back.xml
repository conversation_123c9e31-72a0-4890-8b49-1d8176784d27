<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@id/lay_title"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@id/tv_title"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:includeFontPadding="false"
        android:gravity="center"
        android:paddingLeft="@dimen/margin_left"
        android:layout_marginRight="40dp"
        android:singleLine="true"
        android:ellipsize="end"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_back"
        app:layout_constraintTop_toTopOf="@id/iv_back" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_title_icon"
        android:src="@drawable/ic_back"
        android:tint="@color/fun_n6"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>