package com.ricepo.base.tools

import android.app.Activity
import android.app.ActivityManager
import android.app.ActivityManager.RunningAppProcessInfo
import android.app.usage.UsageStatsManager
import android.content.ContentValues
import android.content.Context
import android.content.pm.PackageInfo
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import com.ricepo.base.BaseApplication
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.util.Calendar
import java.util.UUID

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object SystemUtils {

  private const val UUID_FILENAME = "ricepo_uuid"

  private const val SYSTEM_PREF = "ricepo_pref"

  private fun uuid(): String {
    return "android_${UUID.randomUUID()}"
  }

  /**
   * save uuid to app-specific storage
   * context.openFileOutput (/data/user/0/com.ricepo.app/files/ricepo_uuid)
   */
  fun createUUIDInner(context: Context): String {
    val uuidContent = uuid()
//        context.openFileOutput(UUID_FILENAME, Context.MODE_PRIVATE).use {
//            it.write(uuidContent.toByteArray())
//        }
    val appSpecificExternalDir = File(
      context.getExternalFilesDir(
        Environment.DIRECTORY_DOCUMENTS
      ),
      "${context.packageName}.$UUID_FILENAME"
    )
    if (!appSpecificExternalDir.exists()) {
      appSpecificExternalDir.parentFile?.mkdir()
      appSpecificExternalDir.createNewFile()
    }
    appSpecificExternalDir.bufferedWriter().use { out ->
      out.write(uuidContent)
    }

    return uuidContent
  }

  /**
   * check uuid from app-specific storage
   * /storage/emulated/0/Android/data/com.ricepo.app/files/Documents/ricepo_uuid
   * clear storage also remove ricepo_uuid
   */
  fun checkUUIDInner(context: Context): String? {
//        return context.openFileInput(UUID_FILENAME).bufferedReader().useLines { lines ->
//            lines.fold("") { some, text ->
//                "$some$text"
//            }
//        }
    val appSpecificExternalDir = File(
      context.getExternalFilesDir(
        Environment.DIRECTORY_DOCUMENTS
      ),
      "${context.packageName}.$UUID_FILENAME"
    )
    if (!appSpecificExternalDir.exists()) {
      return null
    }
    return try {
      appSpecificExternalDir.readText()
    } catch (e: Exception) {
      e.printStackTrace()
      null
    }
  }

  /**
   * Checks if a volume containing external storage is available for read and write.
   */
  fun isExternalStorageWritable(): Boolean {
    return Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
  }

  /**
   * Checks if a volume containing external storage is available to at least read.
   */
  fun isExternalStorageReadable(): Boolean {
    return Environment.getExternalStorageState() in
      setOf(Environment.MEDIA_MOUNTED, Environment.MEDIA_MOUNTED_READ_ONLY)
  }

  /**
   * save uuid to share preference
   * clear storage also clear uuid
   */
  fun createUUIDPref(context: Context): String {
    val uuidContent = uuid()

    val sharedPref = context.getSharedPreferences(
      SYSTEM_PREF,
      Context.MODE_PRIVATE
    )
    with(sharedPref.edit()) {
      putString(UUID_FILENAME, uuidContent)
      commit()
    }

    return uuidContent
  }

  /**
   * check uuid from share preference
   */
  fun checkUUIDPref(context: Context): String? {
    val sharedPref = context.getSharedPreferences(
      SYSTEM_PREF,
      Context.MODE_PRIVATE
    )

    return sharedPref.getString(UUID_FILENAME, "")
  }

  /**
   * save uuid to file
   * need write storage permission, android q not used
   */
  @OptIn(ExperimentalStdlibApi::class)
  fun createUUIDFile(context: Context): String {
    val contentValues = ContentValues()
    contentValues.put(MediaStore.Images.Media.DISPLAY_NAME, UUID_FILENAME)
    contentValues.put(MediaStore.Images.Media.MIME_TYPE, "image/*")
    // IS_PENDING = 1 not prepare for item
    contentValues.put(MediaStore.Images.Media.IS_PENDING, 1)
//        contentValues.put(MediaStore.Images.Media.IS_PRIVATE, 1)

    val resolver = context.contentResolver
    val collection = MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)

    val uuidContent = uuid()

    val uri = resolver.insert(collection, contentValues) ?: return uuidContent

    try {
      val fileDescriptor = resolver.openFileDescriptor(uri, "w", null)
      val outputStream = FileOutputStream(fileDescriptor?.fileDescriptor)

      outputStream.write(uuidContent.encodeToByteArray())
      outputStream.close()

      contentValues.clear()
      contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
      resolver.update(uri, contentValues, null, null)
    } catch (fe: FileNotFoundException) {
      fe.printStackTrace()
    } catch (ie: IOException) {
      ie.printStackTrace()
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return uuidContent
  }

  /**
   * check uuid from file
   * need read storage permission, android q not used
   */
  fun checkUUID(context: Context): String {
    val imageUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    val projection = arrayOf<String>(
      MediaStore.Images.Media.DISPLAY_NAME,
      MediaStore.Images.Media._ID
    )

    val contentResolver = context.contentResolver

    val selection = MediaStore.Images.Media.DISPLAY_NAME + "=" + "'" + UUID_FILENAME + "'"
    val cursor = contentResolver.query(imageUri, projection, selection, null, null)

    var uuidContent = ""

    try {
      if (cursor != null) {
        while (cursor.moveToNext()) {
          val fileIdIndex = cursor.getColumnIndex(MediaStore.Images.Media._ID)
          val thumbPath = MediaStore.Images.Media.EXTERNAL_CONTENT_URI.buildUpon()
            .appendPath(cursor.getInt(fileIdIndex).toString()).build().toString()

          val fileUri = Uri.parse(thumbPath)

          val fileDescriptor = contentResolver.openFileDescriptor(fileUri, "r", null)
          val inputStream = FileInputStream(fileDescriptor?.fileDescriptor)
          uuidContent = inputStreamToString(inputStream)

          if (uuidContent.isNotEmpty()) {
            break
          }
        }
        cursor.close()
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }

    return uuidContent
  }

  private fun inputStreamToString(inputStream: FileInputStream): String {
    val baos = ByteArrayOutputStream()
    inputStream.use {
      it.copyTo(baos)
    }
    return baos.toString()
  }

  /**
   * app on the foreground
   */
  fun isAppOnForeground(): Boolean {
    var isOnForground = false
    val activityService = BaseApplication.context.getSystemService(Context.ACTIVITY_SERVICE)
      as ActivityManager
    val runnings = activityService.runningAppProcesses

    for (running in runnings) {
      if (running.processName == BaseApplication.context.packageName) {
        isOnForground =
          (
            running.importance == RunningAppProcessInfo.IMPORTANCE_FOREGROUND ||
              running.importance == RunningAppProcessInfo.IMPORTANCE_VISIBLE
            )
        break
      }
    }

    return isOnForground
  }

  fun getTopActivityName(): String? {
    val context = BaseApplication.context
    val topActivityPackageName: String?
    val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
//        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            if (isSecurityPermissionOpen(context)) {
//                val mUsageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
//                val endTime = System.currentTimeMillis()
//                val beginTime = endTime - 1000 * 60 * 2
//                var recentStats: UsageStats? = null
//                val queryUsageStats = mUsageStatsManager.queryUsageStats(UsageStatsManager.INTERVAL_BEST, beginTime, endTime)
//                if (queryUsageStats == null || queryUsageStats.isEmpty()) {
//                    return null
//                }
//                for (usageStats in queryUsageStats) {
//                    if (recentStats == null || recentStats.lastTimeUsed < usageStats.lastTimeUsed) {
//                        recentStats = usageStats
//                    }
//                }
//                topActivityPackageName = recentStats?.packageName
//                topActivityPackageName
//            } else {
//                null
//            }
//        } else {
    val taskInfos = manager.getRunningTasks(1)
    topActivityPackageName = if (taskInfos.size > 0) taskInfos[0].topActivity?.className else return null
//        }

    return topActivityPackageName
  }

  @SuppressWarnings("ResourceType")
  private fun isSecurityPermissionOpen(context: Context): Boolean {
    val calendar = Calendar.getInstance()
    val endTime = calendar.timeInMillis
    calendar.add(Calendar.YEAR, -2)
    val startTime = calendar.timeInMillis
    val usageStatsManager = context.applicationContext.getSystemService("usagestats") as UsageStatsManager
    val queryUsageStats = usageStatsManager.queryUsageStats(UsageStatsManager.INTERVAL_DAILY, startTime, endTime)
    return !(queryUsageStats == null || queryUsageStats.isEmpty())
  }

  /**
   * get the application version code
   */
  fun versionCode(): Long {
    var code: Long = 0
    try {
      code = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
        packageInfo().longVersionCode
      } else {
        packageInfo().versionCode.toLong()
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return code
  }

  /**
   * get the application version name
   */
  fun versionName(): String {
    var versionName = ""
    try {
      versionName = packageInfo().versionName ?: ""
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return versionName
  }

  private fun packageInfo(): PackageInfo {
    val packageManager = BaseApplication.context.packageManager
    return packageManager.getPackageInfo(BaseApplication.context.packageName, 0)
  }

  fun showMemoryInfo(context: Context) {
    val activityManager = context.getSystemService(Activity.ACTIVITY_SERVICE) as ActivityManager
    val largeMemoryClass = activityManager.largeMemoryClass
    val memoryClass = activityManager.memoryClass

    val memoryInfo = ActivityManager.MemoryInfo()
    activityManager.getMemoryInfo(memoryInfo)

    Log.i("thom", "largeMemoryClass = $largeMemoryClass")
    Log.i("thom", "memoryClass = $memoryClass")

    Log.i("thom", "runtime max = ${Runtime.getRuntime().maxMemory() * 1.0 / (1024 * 1024)}")
    Log.i("thom", "memoryInfo total = ${memoryInfo.totalMem * 1.0 / (1024 * 1024)}")
    Log.i("thom", "memoryInfo available = ${memoryInfo.availMem * 1.0 / (1024 * 1024)}")
    Log.i("thom", "runtime total = ${Runtime.getRuntime().totalMemory() * 1.0 / (1024 * 1024)}")
    Log.i("thom", "runtime free = ${Runtime.getRuntime().freeMemory() * 1.0 / (1024 * 1024)}")
  }
}
