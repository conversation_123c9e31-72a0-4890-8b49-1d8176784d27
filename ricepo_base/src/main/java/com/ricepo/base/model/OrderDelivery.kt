package com.ricepo.base.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.base.parser.Exclude
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Parcelize
data class OrderDelivery(
  @SerializedName("_id")
  val id: String?,
  @Exclude
  var address: Address?,
  val completedAt: String?,
  val createdAt: String?,
  val deadlineAt: String?,
  val dropoffAt: String?,
  val finishAt: String?,
  val updatedAt: String?,
  // restaurant prepare time
  val preparedAt: String?,
  val courier: Courier?,
  val delay: Int?,
  val estimate: Estimate?,
  val fee: Double?,
  val provider: String?,
  val quote: SelfDeliveryQuote?,
  val status: String?,
  val time: Int?,
  val url: String?,
  val driver: DeliveryDriver?,
  val addressUpdated: Boolean? = null,
  val note: InternationalizationContent? = null,
  val proof: ProofInfo? = null
) : Parcelable

@Parcelize
data class SelfDeliveryQuote(
  @SerializedName("_id")
  val id: String,
  val fee: Double?
) : Parcelable

@Parcelize
data class DeliveryDriver(
  @SerializedName("_id")
  val id: String?,
  val email: String?,
  val phone: String?
) : Parcelable

@Parcelize
data class ProofInfo(
  val message: String? = null,
  val photos: List<String>? = null,
) : Parcelable
