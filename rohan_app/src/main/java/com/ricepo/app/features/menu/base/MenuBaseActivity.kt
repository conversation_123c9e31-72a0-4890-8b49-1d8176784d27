package com.ricepo.app.features.menu.base

import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.content.res.Resources
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.IBinder
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import coil.ImageLoader
import coil.decode.GifDecoder
import coil.request.ImageRequest
import coil.request.REPEAT_INFINITE
import com.github.rubensousa.gravitysnaphelper.GravitySnapHelper
import com.ricepo.app.R
import com.ricepo.app.databinding.LayoutMenuCartBarBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.MenuViewModel
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.view.GlobalDialogFacade
import com.ricepo.base.BaseActivity
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.analytics.ScrollDepthFacade
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Restaurant
import com.ricepo.base.tools.CounterDownTimer
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseRecommendMenuEvent
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.animateBottom
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlin.math.abs

//
// Created by Thomsen on 23/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class MenuBaseActivity : BaseActivity() {

  private lateinit var mService: MenuService

  private var mBound: Boolean = false

  protected var menuRestaurant: Restaurant? = null

  private var menuMapper: MenuMapper? = null

  // defines callbacks for service binding, passed to bindService()
  private val connection = object : ServiceConnection {
    override fun onServiceDisconnected(name: ComponentName?) {
      mBound = false
    }

    override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
      // bound to MenuService, cast the IBinder and get MenuService instance
      val binder = service as MenuService.LocalBinder
      mService = binder.getService()
      mBound = true
      checkRestaurantClosed()
      checkGroupOrderCart()
    }
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    // register restaurant closed broadcast
    val filter = IntentFilter()
    filter.addAction(BroadcastConst.ACTION_REFRESH_RESTAURANT_CLOSED)
    registerReceiver(mRestaurantClosedReceiver, filter)

    // bind to MenuService
    Intent(this, MenuService::class.java).also { intent ->
      bindService(intent, connection, Context.BIND_AUTO_CREATE)
    }

    menuMapper = MenuMapper()
  }

  override fun onStart() {
    super.onStart()
    // register group cart broadcast
    val filterCart = IntentFilter()
    filterCart.addAction(BroadcastConst.ACTION_REFRESH_GROUP_ORDER)
    registerReceiver(mGroupOrderCartReceiver, filterCart)
    menuRecommendTimer?.resume()
    menuRecommendGif?.start()
  }

  override fun onResume() {
    super.onResume()
    startGroupOrderCartTimer()
  }

  override fun onPause() {
    super.onPause()
    stopGroupOrderCartTimer()
  }

  override fun onStop() {
    super.onStop()
    // unregister group cart broadcast
    unregisterReceiver(mGroupOrderCartReceiver)
    menuRecommendTimer?.pause()
    menuRecommendGif?.stop()
  }

  override fun onDestroy() {
    super.onDestroy()
    // unregister restaurant closed broadcast
    unregisterReceiver(mRestaurantClosedReceiver)

    unbindService(connection)
    mBound = false
    menuRecommendTimer?.cancel()
    menuRecommendTimer = null
    menuRecommendGif?.recycle()
  }

  /**
   * start the check restaurant closed
   * restart of restaurant changing
   */
  protected fun checkRestaurantClosed() {
    if (mBound) {
      mService.refreshRestaurantTimer(menuRestaurant)
    }
  }

  /**
   * show dialog with restaurant closed changed
   */
  private val mRestaurantClosedReceiver = object : BroadcastReceiver() {
    override fun onReceive(c: Context?, intent: Intent?) {
      val isClosed = intent?.getBooleanExtra(BroadcastConst.PARAM_RESTAURANT_CLOSED, false)
      showClosedDialog(isClosed)
    }
  }

  /**
   * show alert with restaurant closed changed
   */
  fun showClosedDialog(isClosed: Boolean?) {
    val isClosed = isClosed ?: return
    val msg = if (isClosed) {
      ResourcesUtil.getString(com.ricepo.style.R.string.restaurant_closed)
    } else {
      ResourcesUtil.getString(com.ricepo.style.R.string.restaurant_open)
    }
    GlobalDialogFacade.showGlobalDialog(this, msg)
  }

  /**
   * check group cart with group order
   */
  private fun checkGroupOrderCart() {
    if (mBound) {
      mService.startGroupCartTimer()
    }
  }

  private val mGroupOrderCartReceiver = object : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
      refreshGroupOrderCart()
    }
  }

  open fun refreshGroupOrderCart() {}

  /**
   * start the timer to refresh group order cart
   */
  fun startGroupOrderCartTimer() {
    if (mBound) {
      mService.startGroupCartTimer()
    }
  }

  /**
   * stop the timer to refresh group order cart
   */
  fun stopGroupOrderCartTimer() {
    if (mBound) {
      mService.stopGroupCartTimer()
    }
  }

  private var menuRecommendAdapter: MenuSectionAdapter? = null
  private var menuRecommendTimer: CounterDownTimer? = null
  private var menuRecommendGif: GifDrawable? = null
  private var isCanHideRecommend = true

  protected fun initMenuRecommendCollect(
    cartBinding: LayoutMenuCartBarBinding,
    viewModel: MenuViewModel?
  ) {

    // init gif drawable
    menuRecommendGif = GifDrawable(resources, com.ricepo.style.R.drawable.auto_close)
    menuRecommendGif?.setImageView(cartBinding.ivRecommendClose)
    menuRecommendGif?.stop()

    // rcv for recommend
    val gravitySnapHelper = GravitySnapHelper(Gravity.START)
    gravitySnapHelper.maxFlingSizeFraction = 0.8f
    gravitySnapHelper.scrollMsPerInch = 50f
    gravitySnapHelper.attachToRecyclerView(cartBinding.rcvMenuRecommend)
    cartBinding.rcvMenuRecommend.layoutManager = GridLayoutManager(
      cartBinding.root.context, 1, LinearLayoutManager.HORIZONTAL, false
    )
    lifecycleScope.launch {
      viewModel?.menuRecommendChannel?.collectLatest {
        showRecommend(it.isNotEmpty(), cartBinding)

        val uiModels = it.mapIndexed { index, food ->
          MenuUiModel.MenuRecommendItem(food, index)
        }
        menuRecommendAdapter = MenuSectionAdapter(
          uiModels, null,
          addFood = { foodQty ->
            stopHideRecommend()
            lifecycleScope.launch {
              viewModel?.addFood(foodQty.food, foodQty.position, foodQty.foodIndex)
            }

            logRecommendMenuEvent(FirebaseEventName.rMatchAdd)
          },
          minusFood = { foodQty ->
            viewModel?.minusFood(foodQty.food, foodQty.position, foodQty.foodIndex)
          },
          showMore = { _, _, _ ->
          },
          navMenu = {
          },
          navigate = { _ ->
          }
        )
        menuRecommendAdapter?.entrance = entrance
        cartBinding.rcvMenuRecommend.apply {
          adapter = menuRecommendAdapter
          // itemView visibility and alpha invalid
          itemAnimator = null
        }
      }
    }

    cartBinding.clMenuRecommend.setOnTouchListener(
      CartTouchListener { isDown ->
        if (isDown) {
          resetHideRecommend(cartBinding)
          logRecommendMenuEvent(FirebaseEventName.rMatchClose)
        }
      }
    )

    cartBinding.ivRecommendCross.clickWithTrigger {
      resetHideRecommend(cartBinding)
      logRecommendMenuEvent(FirebaseEventName.rMatchClose,)
    }

    cartBinding.rcvMenuRecommend.addOnScrollListener(object : RecyclerView.OnScrollListener() {

      override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        if (dx != 0) {
          stopHideRecommend()
        }
      }

      override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
          startShowRecommend(cartBinding)

          logRecommendMenuEvent(FirebaseEventName.rMatchScroll)
        }
      }
    })
  }

  private fun logRecommendMenuEvent(eventName: String) {
    lifecycleScope.launch {
      AnalyticsFacade.logEvent(
        FirebaseRecommendMenuEvent(
          menuMapper?.mapCartCount(menuRestaurant),
          ScrollDepthFacade.scrollDepth,
        ),
        eventName
      )
    }
  }

  class CartTouchListener(private val upDown: (isDown: Boolean) -> Unit) : View.OnTouchListener {
    var startY = 0f
    var startX = 0f
    var diffY = 0f
    var diffX = 0f

    override fun onTouch(v: View?, event: MotionEvent): Boolean {
      var result = false
      when (event.action) {
        MotionEvent.ACTION_DOWN -> {
          startX = event.x
          startY = event.y
        }
        MotionEvent.ACTION_CANCEL,
        MotionEvent.ACTION_MOVE -> {
          diffX = abs(event.x - startX)
          diffY = event.y - startY
        }
        MotionEvent.ACTION_UP -> {
          if (diffX > abs(diffY)) {
            // horizontal scroll
          } else {
            // up down and click
            upDown(diffY > 5)
          }
        }
      }
      return result
    }
  }

  private fun showRecommend(isShow: Boolean, cartBinding: LayoutMenuCartBarBinding) {
    cartBinding.clMenuRecommend.animateBottom(isShow)
  }

  private fun startShowRecommend(cartBinding: LayoutMenuCartBarBinding) {
    val visible = if (isCanHideRecommend) {
      View.VISIBLE
    } else {
      View.INVISIBLE
    }
    cartBinding.ivRecommendClose.visibility = visible
    var crossSize = DisplayUtil.dp2PxOffset(19f)
    if (isCanHideRecommend) {
      crossSize = DisplayUtil.dp2PxOffset(11f)
    }
    val params = cartBinding.ivRecommendCross.layoutParams
    if (params is ConstraintLayout.LayoutParams) {
      params.width = crossSize
      params.height = crossSize
      cartBinding.ivRecommendCross.layoutParams = params
    }

    // don't time to hide
    if (isCanHideRecommend) else return
    val timeMax = 9.2 * 1000
    menuRecommendTimer?.cancel()
    menuRecommendGif?.reset()
    menuRecommendTimer = object : CounterDownTimer(timeMax.toLong(), 1000) {
      override fun onFinish() {
        resetHideRecommend(cartBinding)
      }

      override fun onTick(millisUntilFinished: Long) {
      }
    }
    menuRecommendTimer?.start()
  }

  private fun stopHideRecommend() {
    menuRecommendTimer?.cancel()
    isCanHideRecommend = false
  }

  fun resetHideRecommend(cartBinding: LayoutMenuCartBarBinding, isHide: Boolean = true) {
    if (isHide) {
      showRecommend(false, cartBinding)
    }
    isCanHideRecommend = true
  }

  protected fun showCart(isShow: Boolean, cartBinding: LayoutMenuCartBarBinding) {
    cartBinding.clMenuCart.animateBottom(isShow)
  }
}

/**
 * Custom GifDrawable implementation using Coil-GIF
 * Maintains compatibility with the original GifDrawable API
 */
class GifDrawable(
  private val resources: Resources,
  private val drawableResId: Int
) : Drawable() {

  private var imageView: ImageView? = null
  private var isStarted = false
  private var isRecycled = false

  fun setImageView(imageView: ImageView) {
    this.imageView = imageView
    loadGif()
  }

  private fun loadGif() {
    val imageView = this.imageView ?: return

    if (isRecycled) return

    // Create ImageLoader with GIF support
    val imageLoader = ImageLoader.Builder(imageView.context)
      .components {
        add(GifDecoder.Factory())
      }
      .build()

    val request = ImageRequest.Builder(imageView.context)
      .data(drawableResId)
      .target(imageView)
      .repeatCount(if (isStarted) REPEAT_INFINITE else 0)
      .build()

    imageLoader.enqueue(request)
  }

  fun start() {
    if (isRecycled) return
    isStarted = true
    loadGif()
  }

  fun stop() {
    isStarted = false
    loadGif()
  }

  fun reset() {
    if (isRecycled) return
    stop()
    start()
  }

  fun recycle() {
    isRecycled = true
    isStarted = false
    imageView = null
  }

  // Drawable abstract methods - minimal implementation
  override fun draw(canvas: android.graphics.Canvas) {
    // Not used in our case as we use ImageView directly
  }

  override fun setAlpha(alpha: Int) {
    // Not used in our case
  }

  override fun setColorFilter(colorFilter: android.graphics.ColorFilter?) {
    // Not used in our case
  }

  override fun getOpacity(): Int {
    return android.graphics.PixelFormat.TRANSLUCENT
  }
}
