package com.ricepo.base

import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.disposables.Disposable

//
// Created by Thomsen on 21/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class BaseUseCase {

  private val disposables = CompositeDisposable()

  protected fun addDisposable(disposable: Disposable) {
    if (!disposable.isDisposed) {
      disposables.add(disposable)
    }
  }

  open fun dispose() {
    disposables.dispose()
  }
}
