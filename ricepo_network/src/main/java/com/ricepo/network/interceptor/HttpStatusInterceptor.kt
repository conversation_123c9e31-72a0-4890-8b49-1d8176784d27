package com.ricepo.network.interceptor

import com.google.gson.GsonBuilder
import com.ricepo.monitor.EventLevel
import com.ricepo.monitor.MonitorEvent
import com.ricepo.monitor.MonitorFacade
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.HttpStatus
import com.ricepo.network.resource.NetworkError
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody

//
// Created by <PERSON><PERSON> on 27/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class HttpStatusInterceptor : Interceptor {

  override fun intercept(chain: Interceptor.Chain): Response {

    var response: Response = chain.proceed(chain.request())
    val body = response.body
    val respContent = body?.string()

    when (response.code) {
      HttpStatus.HTTP_OK,
      HttpStatus.HTTP_CREATED -> {
        handleHttpSuccess(response, respContent)
      }
      HttpStatus.HTTP_NO_CONTENT -> {
        handleHttpSuccess(response, respContent)
      }
      HttpStatus.HTTP_AUTH_FAILED -> {
        handleAuthFailed(response, respContent)
      }
      HttpStatus.HTTP_NOT_FOUND -> {
        handleHttpError(response, respContent)
      }
      HttpStatus.HTTP_BAD_GATEWAY -> {
        handleHttpError(response, respContent)
      }
      HttpStatus.HTTP_INTERNAL_SERVER_ERROR -> {
        handleHttpError(response, respContent)
      }
      else -> handleHttpError(response, respContent)
    }

    return response.newBuilder()
      .body(respContent?.let { ResponseBody.create(body.contentType(), it) })
      .build()
  }

  private fun handleAuthFailed(resp: Response, respContent: String?) {
    handleHttpSuccess(resp, respContent)
  }

  /**
   * handle the network success
   */
  private fun handleHttpSuccess(resp: Response, respContent: String?) {
    var customError: NetworkError? = try {
      GsonBuilder().create().fromJson(respContent, NetworkError::class.java)
    } catch (e: Exception) {
      // response success
      null
    }

    if (customError?.code != null && (customError?.message != null || customError?.error != null)) {
      // monitor business response error
      var message = "code: " + customError.code
      var event = MonitorEvent(message)
      event.extras = mapOf(
        "message" to (customError.message ?: ""),
        "URL" to resp.request.url,
        "statusCode" to resp.code,
        "reqDetails" to resp.request.toString(),
        "respDetails" to (respContent ?: resp.toString())
      )
      event.level = EventLevel.INFO
//      MonitorFacade.captureEvent(event)
      throw customError
    } else {
      if (respContent.isNullOrEmpty()) {
        throw NetworkError(ErrorCode.RESPONSE_BODY_EMPTY)
      }
    }
  }

  /**
   * handle the network error
   */
  private fun handleHttpError(resp: Response, respContent: String?) {
    // decode error from network gateway and response
    var customError: NetworkError? = try {
      GsonBuilder().create().fromJson(respContent, NetworkError::class.java)
    } catch (e: Exception) {
      // response success
      null
    }

    val message = "code: " + (customError?.code ?: "Not From Server Error")
    val event = MonitorEvent(message)
    val url = resp.request.url
    val statusCode = resp.code
    val details = respContent ?: resp.toString()
    event.level = EventLevel.INFO
    event.extras = mapOf(
      "message" to (customError?.message ?: ""),
      "URL" to url,
      "statusCode" to statusCode,
      "reqDetails" to resp.request.toString(),
      "respDetails" to details
    )
//    MonitorFacade.captureEvent(event)

    if (customError != null) {
      throw customError
    }
  }
}
