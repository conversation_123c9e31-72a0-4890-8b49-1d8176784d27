package com.ricepo.base.model.mapper

import com.ricepo.base.data.pref.CommonPref
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.DeliveryZoneFees
import com.ricepo.base.model.Restaurant
import com.ricepo.base.tools.AssetUtils
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.TimeZone
import kotlin.math.abs
import kotlin.math.ceil

//
// Created by <PERSON><PERSON> on 9/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class BaseMapper {

  fun mapCountryCode(restaurant: Restaurant?): String {
    var countryCode = CommonPref.getCountryCode() ?: "US"
    if (restaurant != null && restaurant.address is AddressObj) {
      val address = restaurant.address as AddressObj
      countryCode = address.country ?: CommonPref.getCountryCode() ?: "US"
    }

    return countryCode
  }

  /**
   * format the price
   */
  fun formatPriceByRestaurant(value: Int, restaurant: Restaurant?, toFixed: Int? = 2): String {
    val countryCode = if (restaurant != null) {
      mapCountryCode(restaurant)
    } else {
      CommonPref.getCountryCode()
    }
    return formatPrice(value, countryCode, toFixed)
  }

  /**
   * format the price
   */
  fun formatPrice(value: Int, countryCode: String?, toFixed: Int? = 2): String {
    var currency = AssetUtils.getCountry(countryCode, "sign")

    if (currency == null) {
      currency = ""
    }

    val absValue = abs(value)
    val strValue = if (toFixed != null) {
      // format fixed the decimal point is zero to supplement
      String.format("%.${toFixed}f", (absValue / 100.0))
    } else {
      if (absValue % 100 == 0) {
        (absValue / 100).toString()
      } else {
        (absValue / 100.0).toString()
      }
    }

    val p = "${currency}${if (currency == "€") strValue.replace(".",",") else strValue}"

    if (value < 0) {
      return "($p)"
    }

    return p
  }

  /**
   * get free delivery
   */
  fun getFreeDelivery(fees: List<DeliveryZoneFees?>?, totalPrice: Int? = null): DeliveryZoneFees? {
    if (fees == null) return null

    if (totalPrice != null) {
      return fees.filter { totalPrice >= (it?.minimum ?: 0.0) }
        .maxByOrNull { it?.minimum ?: 0.0 }
    }

    return fees.filter { it?.adjustments?.customer ?: 0 > 0 }
      .minByOrNull { it?.minimum ?: 0.0 }
  }

  /**
   * format: 'hh:mm a'
   *  'h:mm a (EEE)'
   */
  fun formatTime(origin: String?, format: String): String? {
    if (origin == null) return null
    val originFormatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    originFormatter.timeZone = TimeZone.getTimeZone("UTC")
    try {
      val date = originFormatter.parse(origin)
      val dateFormatter = SimpleDateFormat(format)
      return dateFormatter.format(date)
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return ""
  }

  /**
   * to current date from utc time
   */
  fun toDate(origin: String?): Calendar? {
    val origin = origin ?: return null
    val originFormatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    originFormatter.timeZone = TimeZone.getTimeZone("UTC")
    try {
      val date = originFormatter.parse(origin)
      val cal = Calendar.getInstance()
      cal.time = date
      return cal
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return null
  }

  /**
   * to local current date from utc time
   */
  private fun toDateLocal(origin: String?): Calendar? {
    val origin = origin ?: return null
    val originFormatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    originFormatter.timeZone = TimeZone.getDefault()
    try {
      val date = originFormatter.parse(origin)
      val cal = Calendar.getInstance()
      cal.time = date
      return cal
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return null
  }

  /**
   * diff days after now by time seconds
   */
  fun daysOnNow(createdAt: String?): Long? {
    val createdAt = createdAt ?: return null
    val createdDate = toDateLocal(createdAt)
    if (createdDate != null) {
      val now = Calendar.getInstance()
      val millis = now.timeInMillis - createdDate.timeInMillis
      return ceil(millis / (1000 * 3600 * 24.0)).toLong()
    }
    return null
  }

  /**
   * calc reward coin count
   */
  fun calcCoinCount(point: Int?): Int {
    return (point ?: 0) / 100
  }
}
