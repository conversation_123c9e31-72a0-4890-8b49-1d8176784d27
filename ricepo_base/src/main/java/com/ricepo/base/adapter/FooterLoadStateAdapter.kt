package com.ricepo.base.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.view.isVisible
import androidx.paging.LoadState
import androidx.paging.LoadStateAdapter
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.base.R
import com.ricepo.base.databinding.LoadStateFooterViewBinding
import com.ricepo.base.extension.clickWithTrigger

//
// Created by <PERSON><PERSON> on 3/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class FooterLoadStateAdapter(private val retry: () -> Unit) : LoadStateAdapter<RecyclerView.ViewHolder>() {

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, loadState: LoadState) {
    if (holder is LoadStateViewHolder) {
      holder.bind(loadState)
    }
//        if (loadState is LoadState.Error) {
//            Handler().post {
//                this.loadState = LoadState.NotLoading(endOfPaginationReached = false)
//            }
//        }
  }

  override fun onCreateViewHolder(parent: ViewGroup, loadState: LoadState): RecyclerView.ViewHolder {
    return LoadStateViewHolder.create(parent, retry)
  }

  override fun displayLoadStateAsItem(loadState: LoadState): Boolean {
//        return loadState is LoadState.Loading
    return super.displayLoadStateAsItem(loadState)
  }
}

class LoadStateViewHolder(
  private val binding: LoadStateFooterViewBinding,
  private val retry: () -> Unit
) : RecyclerView.ViewHolder(binding.root) {

  init {
    binding.tvRetry.clickWithTrigger {
      retry()
    }
  }

  fun bind(loadState: LoadState) {
    val loadingVisible = (
      loadState is LoadState.Loading ||
        (loadState is LoadState.NotLoading && !loadState.endOfPaginationReached)
      )
//        if (loadingVisible) {
//            binding.progressBar.visibility = View.VISIBLE
//        } else {
//            binding.progressBar.visibility = View.INVISIBLE
//        }
    binding.progressBar.isVisible = loadingVisible

//        binding.tvRetry.isVisible = (loadState is LoadState.Error)

//        if (loadState is LoadState.Error) {
//            binding.progressBar.visibility = View.VISIBLE
//            Handler().post {
//                retry()
//            }
//        }
  }

  companion object {
    fun create(parent: ViewGroup, retry: () -> Unit): LoadStateViewHolder {
      val view = LayoutInflater.from(parent.context)
        .inflate(R.layout.load_state_footer_view, parent, false)
      val binding = LoadStateFooterViewBinding.bind(view)
      val params = LinearLayout.LayoutParams(
        LinearLayout.LayoutParams.MATCH_PARENT,
        LinearLayout.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      return LoadStateViewHolder(binding, retry)
    }
  }
}
