package com.ricepo.monitor.firebase

import android.util.Log
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson

//
// Created by <PERSON><PERSON> on 16/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object FirebaseMonitor {

  private val firebaseAnalytics: FirebaseAnalytics = Firebase.analytics

  fun logEvent(name: String?, event: FirebaseEvent?) {
    val eventName = name ?: return
    val event = event ?: return
    firebaseAnalytics.logEvent(eventName, event.toParams().bundle)
    Log.i(
      "FirebaseMonitor",
      "eventName: $eventName [ ${
      Gson().toJson(event)} ]"
    )
  }
}
