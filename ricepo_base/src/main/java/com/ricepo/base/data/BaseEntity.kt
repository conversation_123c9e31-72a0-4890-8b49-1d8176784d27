package com.ricepo.base.data

import androidx.room.ColumnInfo
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.ricepo.base.data.converter.DateConverter
import com.ricepo.base.parser.Exclude
import java.util.Date

//
// Created by <PERSON><PERSON> on 13/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
abstract class BaseEntity {

  // solved You must annotate primary keys with @NonNull.
  @PrimaryKey(autoGenerate = true)
  @ColumnInfo(name = "id")
  @Exclude
  private var id: Long = 0

  @ColumnInfo(name = "created_at", defaultValue = "CURRENT_TIMESTAMP")
  @TypeConverters(DateConverter::class)
  private var createdAt: Date? = null

  @ColumnInfo(name = "updated_at", defaultValue = "CURRENT_TIMESTAMP")
  @TypeConverters(DateConverter::class)
  private var updatedAt: Date? = null

  open fun getId(): Long {
    return id
  }

  open fun setId(id: Long) {
    this.id = id
  }

  open fun getCreatedAt(): Date? {
    return createdAt
  }

  open fun setCreatedAt(createdAt: Date?) {
    this.createdAt = createdAt
  }

  open fun getUpdatedAt(): Date? {
    return updatedAt
  }

  open fun setUpdatedAt(updatedAt: Date?) {
    this.updatedAt = updatedAt
  }
}
