package com.ricepo.base.extension

import android.graphics.Paint
import android.graphics.Rect
import android.text.Spannable
import android.text.SpannableString
import android.text.style.UnderlineSpan
import android.view.MotionEvent
import android.view.TouchDelegate
import android.view.View
import android.view.ViewTreeObserver
import android.view.inputmethod.EditorInfo
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import com.ricepo.base.R
import com.ricepo.base.inputmanager.KeyboardUtil
import kotlin.math.abs

//
// Created by <PERSON><PERSON> on 13/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * This listener is available for any view’s ViewTreeObserver and its
 * quite often used to get a callback when the view layout
 */
inline fun View.waitForLayout(crossinline f: () -> Unit) = with(viewTreeObserver) {
  addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
    override fun onGlobalLayout() {
      viewTreeObserver.removeOnGlobalLayoutListener(this)
      f()
    }
  })
}

/**
 * This listener is available for any view’s ViewTreeObserver and its
 * quite often used to get a callback when the view is inflated and measured
 */
inline fun View.waitForMeasured(crossinline f: () -> Unit) = with(viewTreeObserver) {
  addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
    override fun onGlobalLayout() {
      // invalid of constraintlayout height 0dp
      if (measuredHeight > 0 && measuredWidth > 0) {
        viewTreeObserver.removeOnGlobalLayoutListener(this)
        f()
      }
    }
  })
}

/**
 * callback with redraw
 */
inline fun View.waitForDraw(crossinline f: () -> Unit) = with(viewTreeObserver) {
  addOnDrawListener(object : ViewTreeObserver.OnDrawListener {
    override fun onDraw() {
      f()
    }
  })
}

/**
 */
private var <T : View>T.triggerLastTime: Long
  get() = if (getTag(R.id.trigger_last_time_key) != null) getTag(R.id.trigger_last_time_key) as Long else 0
  set(value) {
    setTag(R.id.trigger_last_time_key, value)
  }

/**
 */
private var <T : View> T.triggerDelay: Long
  get() = if (getTag(R.id.trigger_delay_key) != null) getTag(R.id.trigger_delay_key) as Long else -1
  set(value) {
    setTag(R.id.trigger_delay_key, value)
  }

/**
 */
private fun <T : View> T.clickEnable(): Boolean {
  var clickable = false
  val currentClickTime = System.currentTimeMillis()
  if (currentClickTime - triggerLastTime >= triggerDelay) {
    clickable = true
  }
  triggerLastTime = currentClickTime
  return clickable
}

/***
 *  delay click with trigger
 */
fun <T : View> T.clickWithTrigger(delay: Long = 500, block: (T) -> Unit) {
  triggerDelay = delay
  setOnClickListener {
    if (clickEnable()) {
      block(this)
    }
  }
}

/**
 * delay touch click with trigger action up
 */
fun <T : View> T.touchWithTrigger(delay: Long = 500, block: (T, event: MotionEvent) -> Unit) {
  triggerDelay = delay
  setOnTouchListener { v, event ->
    if (event.action == MotionEvent.ACTION_UP) {
      if (clickEnable()) {
        block(this, event)
      }
    } else {
//            block(this, event)
    }
    true
  }
}

private var isClick = true
private var startX = 0f
private var startY = 0f

/**
 * delay touch click and resolving the move conflict
 */
fun <T : View> T.touchWithClickTrigger(delay: Long = 500, block: (T, event: MotionEvent) -> Unit) {
  triggerDelay = delay
  setOnTouchListener { v, event ->
    when (event.action) {
      MotionEvent.ACTION_DOWN -> {
        isClick = true
        startX = event.x
        startY = event.y
        false
      }
      MotionEvent.ACTION_MOVE -> {
        if (abs(event.x - startX) > 5 || abs(event.y - startY) > 5) {
          isClick = false
        }
        false
      }
      MotionEvent.ACTION_UP -> {
        if (isClick && clickEnable()) {
          block(this, event)
          true
        } else {
          false
        }
      }
      else -> {
        // default don't consumer event
        false
      }
    }
  }
}

/**
 * delay touch click and resolving the move conflict
 */
fun <T : View> T.touchWithClickLongTrigger(
  delay: Long = 500,
  click: (T, event: MotionEvent) -> Unit,
  longClick: () -> Unit
) {
  triggerDelay = delay
  var downTime = 0L
  setOnTouchListener { v, event ->
    when (event.action) {
      MotionEvent.ACTION_DOWN -> {
        isClick = true
        startX = event.x
        startY = event.y
        downTime = System.currentTimeMillis()
        true
      }
      MotionEvent.ACTION_MOVE -> {
        if (abs(event.x - startX) > 5 || abs(event.y - startY) > 5) {
          isClick = false
        }
        false
      }
      MotionEvent.ACTION_UP -> {
        val now = System.currentTimeMillis()
        if (now - downTime > 3000) {
          longClick()
          true
        } else if (isClick && clickEnable()) {
          click(this, event)
          true
        } else {
          false
        }
      }
      else -> {
        // default don't consumer event
        false
      }
    }
  }
}

/**
 * click with group in constraint layout
 */
fun Group.clickAllWithTrigger(delay: Long = 500, block: (View) -> Unit) {
  referencedIds?.forEach { id ->
    rootView.findViewById<View>(id)?.clickWithTrigger {
      block(this)
    }
  }
}

fun Group.applyStyle(style: (View) -> Unit) {
  referencedIds?.forEach { id ->
    rootView.findViewById<View>(id)?.apply {
      style(this)
    }
  }
}

/**
 * the under line of TextView
 */
fun TextView.underline() {
  this.paint.flags = Paint.UNDERLINE_TEXT_FLAG
  this.paint.isAntiAlias = true
}

/**
 * the under line of TextView by UnderlineSpan
 */
fun TextView.underlineSpan() {
  val spanText = SpannableString(this.text)
  spanText.setSpan(
    UnderlineSpan(), 0, spanText.length,
    Spannable.SPAN_INCLUSIVE_EXCLUSIVE
  )
  this.text = spanText
}

/**
 * the edittext search action
 */
fun EditText.searchAction(search: (String) -> Unit) {
  this.setOnEditorActionListener { v, actionId, event ->
    if (actionId == EditorInfo.IME_ACTION_SEARCH) {
      KeyboardUtil.hideKeyboard(v.context, v)
      search(v.text.toString())
      true
    } else {
      false
    }
  }
}

/**
 * expand the view touch region
 * 1. Invalidate the part beyond parent
 * 2. A view last set to TouchDelegate is valid.
 */
fun Button.delegateTouch(): Button {
  val parent = this.parent as View
  this.post {
    val rect = Rect()
    this.getHitRect(rect)
    val density = this.resources.displayMetrics.density
    rect.left -= (4 * density).toInt()
    rect.top -= (20 * density).toInt()
    rect.right += (50 * density).toInt()
    rect.bottom += (50 * density).toInt()
    parent.touchDelegate = TouchDelegate(rect, this)
  }
  return this
}

fun View.touchEventInView(x: Float, y: Float): Boolean {
  val view = this
  val location = IntArray(2) { 0 }
  view.getLocationOnScreen(location)

  val left = location[0]
  val top = location[0]

  val right = left + view.measuredWidth
  val bottom = top + view.measuredHeight

  if (y >= top && y <= bottom && x >= left && x <= right) {
    return true
  }
  return false
}

fun TextView.onRightDrawableListener(rightClick: () -> Unit) {
  this.setOnTouchListener { v, event ->
    if (event.action == MotionEvent.ACTION_DOWN) {
      val drawableRight = this.compoundDrawables[2]
      // rawX relative to screen
      // x relative to view
      if (drawableRight != null && event.x >=
        (this.right - drawableRight.bounds.width())
      ) {
        rightClick()
        true
      } else {
        false
      }
    } else {
      false
    }
  }
}
