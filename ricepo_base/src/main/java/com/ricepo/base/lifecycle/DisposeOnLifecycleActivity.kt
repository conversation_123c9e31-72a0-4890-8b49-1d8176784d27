package com.ricepo.base.lifecycle

import io.reactivex.rxjava3.disposables.Disposable

//
// Created by <PERSON><PERSON> on 20/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

interface DisposeOnLifecycleActivity {

  val lifecycleDisposables: LifecycleDisposables

  fun onPause() = lifecycleDisposables.dispose(LifecycleDisposables.State.Pause)
  fun onStop() = lifecycleDisposables.dispose(LifecycleDisposables.State.Stop)
  fun onDestroy() = lifecycleDisposables.dispose(LifecycleDisposables.State.Destroy)

  fun Disposable.untilPause() = also {
    lifecycleDisposables.add(LifecycleDisposables.State.Pause, it)
  }

  fun Disposable.untilStop() = also {
    lifecycleDisposables.add(LifecycleDisposables.State.Stop, it)
  }

  fun Disposable.untilDestroy() = also {
    lifecycleDisposables.add(LifecycleDisposables.State.Destroy, it)
  }
}
