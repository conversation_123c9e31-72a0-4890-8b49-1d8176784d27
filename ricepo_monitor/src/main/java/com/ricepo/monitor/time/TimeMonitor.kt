package com.ricepo.monitor.time

import android.util.Log

//
// Created by <PERSON><PERSON> on 1/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class TimeMonitor(mMonitorId: Int) {

  private val TAG = TimeMonitor::class.java.simpleName

  var monitorId = -1
  val timeTags =
    HashMap<String, Long?>()
  private var mStartTime: Long = 0

  fun startMonitor() {
    if (timeTags.size > 0) {
      timeTags.clear()
    }
    mStartTime = System.currentTimeMillis()
  }

  /**
   */
  fun recordingTimeTag(tag: String) {
    if (timeTags[tag] != null) {
      timeTags.remove(tag)
    }
    val time = System.currentTimeMillis() - mStartTime
    Log.d(TAG, "$tag: $time")
    timeTags[tag] = time
  }

  fun end(tag: String, writeLog: Boolean) {
    recordingTimeTag(tag)
    end(writeLog)
  }

  fun end(writeLog: Boolean) {
    if (writeLog) {
      // write local
    }
  }

  init {
    Log.d(TAG, "init TimeMonitor id: $mMonitorId")
    monitorId = mMonitorId
  }
}
