package com.ricepo.base.data.common.kv

//
// Created by <PERSON><PERSON> on 20/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

const val KEY_RESTAURANT_CART = "restaurant_cart"

// the cart of market
const val KEY_RESTAURANT_CART_MARKET = "restaurant_cart_market"

const val KEY_AUTH_TOKEN = "token"

const val KEY_CUSTOMER = "customer"

const val KEY_PAYMENT = "payment"

/**
 * wechat payment callback with order
 */
const val KEY_ORDER = "order"

/**
 * order detail in order page
 */
const val KEY_REFRESH_ORDER = "order_refresh"

/**
 * group order
 */
const val KEY_GROUP_ORDER = "group_order"

const val KEY_REQUIRE_PERMISSION = "require_permissin"
