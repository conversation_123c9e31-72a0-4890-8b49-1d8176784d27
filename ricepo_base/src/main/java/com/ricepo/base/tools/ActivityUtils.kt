package com.ricepo.base.tools

import android.app.Activity
import android.content.Context
import android.os.Build

//
// Created by <PERSON><PERSON> on 2021/10/11.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
object ActivityUtils {

  /**
   * determine if the activity is destroyed
   */
  fun isActivityDestroy(context: Context?): <PERSON><PERSON><PERSON> {
    if (context == null) return false
    return if (context is Activity) {
      context == null || context.isFinishing ||
        (
          Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 &&
            context.isDestroyed
          )
    } else {
      false
    }
  }
}
