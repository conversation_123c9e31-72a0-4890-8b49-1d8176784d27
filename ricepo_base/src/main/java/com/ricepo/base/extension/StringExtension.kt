package com.ricepo.base.extension

import android.text.TextPaint
import com.ricepo.style.ResourcesUtil
import kotlin.math.floor

//
// Created by <PERSON><PERSON> on 26/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * the [count] blank space index of wrap line
 */
fun String.formatWrap(count: Int): String {
  val arr = this.split(" ")
  val size = arr.size
  return if (size > count) {
    val index = this.indexOf(arr[count])
    StringBuilder(this.substring(0, index)).insert(index, "\n")
      .append(this.trim().substring(index).formatWrap(count))
      .toString()
  } else {
    this
  }
}

/**
 * the max [lineWidth] for line length to evenly divide
 */
fun String.formatEvenly(lineWidth: Int): String {
  return if (this.contains("\n")) {
    this
  } else {
    val textPaint = TextPaint()
    val strWidth = textPaint.measureText(this)
    // evenly divide
    if (strWidth > lineWidth) {
      val middleIndex = floor(length / 2.0).toInt()
      val blankIndex = substring(0, middleIndex).lastIndexOf(" ")
      if (blankIndex > 0) {
        StringBuilder(this).insert(blankIndex, "\n")
          .toString()
      } else {
        StringBuilder(this).insert(middleIndex, "\n")
          .toString()
      }
    } else {
      this
    }
  }
}

/**
 * international resources string
 */
fun Int.localized(): String {
  return ResourcesUtil.getString(this)
}
