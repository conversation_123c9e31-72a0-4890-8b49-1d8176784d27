package com.ricepo.base.loading

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.os.Debug
import androidx.appcompat.app.AppCompatActivity
import com.ricepo.base.animation.LoadingView
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

//
// Created by <PERSON><PERSON> on 3/9/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class LoadingActivity : AppCompatActivity() {

  private val mCloseReceiver = object : BroadcastReceiver() {
    override fun onReceive(c: Context?, intent: Intent?) {
      finish()
    }
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    val filter = IntentFilter()
    filter.addAction("aaa")
    registerReceiver(mCloseReceiver, filter)

    loadingView = LoadingView(this)
    setContentView(loadingView)
    showLoading()
  }

  override fun onPause() {
    // cancel animation with finish event
    overridePendingTransition(0, 0)
    super.onPause()
    hideLoading()
  }

  override fun onDestroy() {
    super.onDestroy()
    unregisterReceiver(mCloseReceiver)
  }

  // Avoid triggering layout during animations.
  private var loadingView: LoadingView? = null

  private fun showLoading() {
    // starts recording a trace log
    val dateFormat: DateFormat = SimpleDateFormat("dd_MM_yyyy_hh_mm_ss", Locale.getDefault())
    val logDate: String = dateFormat.format(Date())
    Debug.startMethodTracing("loading-$logDate")
    loadingView?.showLoading()
  }

  private fun hideLoading() {
    if (loadingView != null) {

      loadingView?.clear()

      loadingView = null
      Debug.stopMethodTracing()
    }
  }

  override fun registerReceiver(receiver: BroadcastReceiver?, filter: IntentFilter?): Intent? {
    if (Build.VERSION.SDK_INT >= 34 && applicationInfo.targetSdkVersion >= 34) {
      return super.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED)
    } else {
      return super.registerReceiver(receiver, filter)
    }
  }
}
