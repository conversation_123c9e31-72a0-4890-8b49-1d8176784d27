package com.ricepo.network.executor

import android.os.Looper
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 27/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class LooperThread @Inject constructor() : Thread() {

  private lateinit var looper: Looper

  fun getLooper(): Looper {
    return looper
  }

  override fun run() {
    Looper.prepare()
    looper = Looper.myLooper() ?: return
    Looper.loop()
  }
}
