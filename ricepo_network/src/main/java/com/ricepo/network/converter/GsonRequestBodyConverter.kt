package com.ricepo.network.converter

import com.google.gson.Gson
import com.google.gson.TypeAdapter
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okio.Buffer
import retrofit2.Converter
import java.io.IOException
import java.io.OutputStreamWriter
import java.io.Writer
import java.nio.charset.Charset

//
// Created by <PERSON><PERSON> on 25/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class GsonRequestBodyConverter(private val gson: Gson, private val adapter: TypeAdapter<in Any>) :
  Converter<Any, RequestBody> {

  @Throws(IOException::class)
  override fun convert(value: Any): RequestBody {
    val buffer = Buffer()
    val writer: Writer =
      OutputStreamWriter(buffer.outputStream(), UTF_8)
    val jsonWriter = gson.newJsonWriter(writer)
    adapter.write(jsonWriter, value)
    jsonWriter.close()
    return RequestBody.create(MEDIA_TYPE, buffer.readByteString())
  }

  companion object {
    private val MEDIA_TYPE = "application/json; charset=UTF-8".toMediaTypeOrNull()
    private val UTF_8 = Charset.forName("UTF-8")
  }
}
