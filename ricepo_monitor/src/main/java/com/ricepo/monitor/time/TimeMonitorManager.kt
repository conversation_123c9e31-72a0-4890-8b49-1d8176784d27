package com.ricepo.monitor.time

//
// Created by <PERSON><PERSON> on 1/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class TimeMonitorManager {
  private var mTimeMonitorMap: HashMap<Int, TimeMonitor?> = hashMapOf<Int, TimeMonitor?>()

  /**
   */
  fun resetTimeMonitor(id: Int) {
    if (mTimeMonitorMap[id] != null) {
      mTimeMonitorMap.remove(id)
    }
    getTimeMonitor(id)
  }

  /**
   */
  fun getTimeMonitor(id: Int): TimeMonitor {
    var monitor = mTimeMonitorMap[id]
    if (monitor == null) {
      monitor = TimeMonitor(id)
      mTimeMonitorMap[id] = monitor
    }
    return monitor
  }

  companion object {
    private var mTimeMonitorManager: TimeMonitorManager = TimeMonitorManager()

    @get:Synchronized
    val instance: TimeMonitorManager
      get() {
        if (mTimeMonitorManager == null) {
          mTimeMonitorManager = TimeMonitorManager()
        }
        return mTimeMonitorManager
      }
  }

  init {
    mTimeMonitorMap = HashMap()
  }
}
