package com.ricepo.base.extension

import java.text.SimpleDateFormat
import java.util.Calendar

//
// Created by <PERSON><PERSON> on 23/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * format the calendar to string
 * @param pattern "yyyy-MM-dd HH:mm:ss" etc.
 */
fun Calendar.format(pattern: String): String {
  val simpleFormat = SimpleDateFormat(pattern)
  return simpleFormat.format(this.time)
}
