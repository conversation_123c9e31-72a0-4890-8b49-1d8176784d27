package com.ricepo.base.model.parser

import com.google.gson.JsonElement
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.ricepo.base.model.Food
import com.ricepo.base.model.FoodImage
import com.ricepo.base.model.Option
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 21/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class FoodSerializer : JsonSerializer<Food> {

  override fun serialize(src: Food?, typeOfSrc: Type?, context: JsonSerializationContext?): JsonElement {

    val jsonElement = ParserFacade.gson.toJsonTree(src)

    val option = src?.options
    if (option is List<Option>) {
      jsonElement.asJsonObject.add("options", ParserFacade.gson.toJsonTree(option))
    } else {
      jsonElement.asJsonObject.add("options", context?.serialize(option))
    }

//        val score = src?.score
//        if (score is Score) {
//            jsonElement.asJsonObject.add("score", ParserFacade.gson.toJsonTree(score))
//        } else {
//            jsonElement.asJsonObject.add("score", context?.serialize(score))
//        }

    val image = src?.image
    if (image is FoodImage) {
      jsonElement.asJsonObject.add("image", ParserFacade.gson.toJsonTree(image))
    } else {
      jsonElement.asJsonObject.add("image", context?.serialize(image))
    }

    return jsonElement
  }
}
