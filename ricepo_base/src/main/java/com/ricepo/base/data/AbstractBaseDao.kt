package com.ricepo.base.data

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Update
import java.util.Date

//
// Created by <PERSON><PERSON> on 13/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Dao
abstract class AbstractBaseDao<T : BaseEntity> {

  /**
   * Insert a entity in the database. If the entity already exists, replace it.
   * @param entity the entity to be inserted.
   */
  @Insert(onConflict = OnConflictStrategy.REPLACE)
  abstract fun actualInsert(t: T): Long

  open fun insert(t: T): Long {
    t.setUpdatedAt(Date())
    return actualInsert(t)
  }

  @Insert
  abstract fun actualInsertAll(ts: List<T>): List<Long?>?

  open fun insertAll(ts: List<T>): List<Long?>? {
    if (ts != null) {
      for (t in ts) {
        t.setCreatedAt(Date())
        t.setUpdatedAt(Date())
      }
    }
    return actualInsertAll(ts)
  }

  @Update
  abstract fun actualUpdate(t: T)

  open fun update(t: T) {
    t.setUpdatedAt(Date())
    actualUpdate(t)
  }

  @Update
  abstract fun actualUpdateAll(ts: List<T>)

  open fun updateAll(ts: List<T>) {
    if (ts != null) {
      for (t in ts) {
        t.setUpdatedAt(Date())
      }
    }
    actualUpdateAll(ts)
  }

  @Delete
  abstract fun delete(t: T)

  @Delete
  abstract fun deleteAll(ts: List<T>)
}
