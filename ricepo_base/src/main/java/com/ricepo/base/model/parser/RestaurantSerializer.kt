package com.ricepo.base.model.parser

import com.google.gson.JsonElement
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.RequireTip
import com.ricepo.base.model.Restaurant
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 30/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantSerializer : JsonSerializer<Restaurant> {

  override fun serialize(src: Restaurant?, typeOfSrc: Type?, context: JsonSerializationContext?): JsonElement {

    val jsonElement = ParserFacade.gson.toJsonTree(src)

    val address = src?.address
    if (address is AddressObj) {
      jsonElement.asJsonObject.add("address", ParserFacade.gson.toJsonTree(address))
    } else {
      jsonElement.asJsonObject.add("address", context?.serialize(address))
    }

    val requireTip = src?.requireTip
    if (requireTip is RequireTip.double) {
      jsonElement.asJsonObject.add("requireTip", context?.serialize(requireTip.v1))
    } else if (requireTip != null) {
      try {
        requireTip as RequireTip.bool
        jsonElement.asJsonObject.add("requireTip", context?.serialize(requireTip.v1))
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }

    return jsonElement
  }
}
