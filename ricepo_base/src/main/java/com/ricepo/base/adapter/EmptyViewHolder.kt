package com.ricepo.base.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.style.view.headerlayout.SectionHolder
import com.ricepo.base.R

//
// Created by <PERSON><PERSON> on 7/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class EmptyViewHolder private constructor(view: View) : RecyclerView.ViewHolder(view) {

  companion object {
    fun create(parent: ViewGroup): EmptyViewHolder {
      val view = LayoutInflater.from(parent.context)
        .inflate(R.layout.layout_empty_holder, parent, false)
      return EmptyViewHolder(view)
    }
  }
}

class EmptySectionHolder private constructor(view: View) : SectionHolder(view) {

  companion object {
    fun create(parent: ViewGroup): EmptySectionHolder {
      val view = LayoutInflater.from(parent.context)
        .inflate(R.layout.layout_empty_holder, parent, false)
      return EmptySectionHolder(view)
    }
  }
}
