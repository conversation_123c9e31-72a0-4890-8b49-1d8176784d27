package com.ricepo.base.inputmanager

import android.R
import android.app.Activity
import android.graphics.Rect
import android.view.View
import android.widget.FrameLayout

//
// Created by <PERSON><PERSON> on 15/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class AndroidBug5497Workaround private constructor(activity: Activity) {

  private val mChildOfContent: View?
  private var usableHeightPrevious = 0
  private val frameLayoutParams: FrameLayout.LayoutParams

  private fun possiblyResizeChildOfContent() {
    val usableHeightNow = computeUsableHeight()
    if (usableHeightNow != usableHeightPrevious) {
      val usableHeightSansKeyboard = mChildOfContent?.rootView?.height ?: return
      val heightDifference = usableHeightSansKeyboard - usableHeightNow
      if (heightDifference > usableHeightSansKeyboard / 4) {
        frameLayoutParams.height = usableHeightSansKeyboard - heightDifference
      } else {
        frameLayoutParams.height = usableHeightSansKeyboard
      }
      mChildOfContent?.requestLayout()
      usableHeightPrevious = usableHeightNow
    }
  }

  private fun computeUsableHeight(): Int {
    val r = Rect()
    mChildOfContent?.getWindowVisibleDisplayFrame(r)
    return r.bottom - r.top
  }

  companion object {
    fun assistActivity(activity: Activity) {
      AndroidBug5497Workaround(activity)
    }
  }

  init {
    val content = activity.findViewById<View>(R.id.content) as FrameLayout
    mChildOfContent = content.getChildAt(0)
    mChildOfContent?.viewTreeObserver?.addOnGlobalLayoutListener { possiblyResizeChildOfContent() }
    frameLayoutParams = mChildOfContent?.layoutParams as FrameLayout.LayoutParams
  }
}
