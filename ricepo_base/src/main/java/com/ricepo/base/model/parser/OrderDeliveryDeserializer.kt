package com.ricepo.base.model.parser

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.AddressStr
import com.ricepo.base.model.OrderDelivery
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OrderDeliveryDeserializer : JsonDeserializer<OrderDelivery> {
  override fun deserialize(
    json: JsonElement?,
    typeOfT: Type?,
    context: JsonDeserializationContext?
  ): OrderDelivery {

    json as JsonObject

    val gson = ParserFacade.gson

    val delivery = gson.fromJson(json, OrderDelivery::class.java)

    var addressElement = json.get("address")

    if (addressElement != null) {
      if (addressElement.isJsonObject) {
        val addressObj = gson.fromJson(addressElement, AddressObj::class.java)
        delivery.address = addressObj
      } else {
        delivery.address = AddressStr(addressElement.toString())
      }
    }

    return delivery
  }
}
