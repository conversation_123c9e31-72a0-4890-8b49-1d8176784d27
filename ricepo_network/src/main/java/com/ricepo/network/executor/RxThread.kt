package com.ricepo.network.executor

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Scheduler
import io.reactivex.rxjava3.schedulers.Schedulers
import javax.inject.Inject

class RxThread @Inject constructor(val looperThread: LooperThread) : PostExecutionThread {

  override val mainScheduler: Scheduler
    get() = AndroidSchedulers.mainThread()

  override val ioScheduler: Scheduler
    get() = Schedulers.io()

  override val loopedIoScheduler: Scheduler
    get() = AndroidSchedulers.from(looperThread.getLooper())

  override val computationScheduler: Scheduler
    get() = Schedulers.computation()
}
