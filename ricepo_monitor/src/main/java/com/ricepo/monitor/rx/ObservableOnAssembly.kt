/*
 * Copyright 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ricepo.monitor.rx

import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.ObservableSource
import io.reactivex.rxjava3.core.Observer
import io.reactivex.rxjava3.internal.fuseable.QueueFuseable
import io.reactivex.rxjava3.internal.observers.BasicFuseableObserver

/**
 * Wraps a ObservableSource and inject the assembly info.
 *
 * @param <T> the value type
</T> */
internal class ObservableOnAs<PERSON>mbly<T : Any>(val source: ObservableSource<T>) :
  Observable<T>() {
  val assembled: RxJavaAssemblyException
  override fun subscribeActual(observer: Observer<in T>) {
    source.subscribe(OnAssemblyObserver(observer, assembled))
  }

  internal class OnAssemblyObserver<T : Any>(
    downstream: Observer<in T>?,
    val assembled: RxJavaAssemblyException
  ) : BasicFuseableObserver<T, T>(downstream) {
    override fun onNext(t: T) {
      downstream.onNext(t)
    }

    override fun onError(t: Throwable) {
      assembled.appendLast(t)?.let { downstream.onError(it) }
    }

    override fun requestFusion(mode: Int): Int {
      val qd = qd
      if (qd != null) {
        val m = qd.requestFusion(mode)
        sourceMode = m
        return m
      }
      return QueueFuseable.NONE
    }

    @Throws(Throwable::class)
    override fun poll(): T? {
      return qd.poll()
    }
  }

  init {
    assembled = RxJavaAssemblyException()
  }
}
