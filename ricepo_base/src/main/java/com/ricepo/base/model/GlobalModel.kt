package com.ricepo.base.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.base.parser.Exclude
import com.ricepo.style.LocaleConst
import com.ricepo.style.LocaleUtil
import kotlinx.parcelize.Parcelize

interface I18n {
  val enUS: String?
  val zhCN: String?
  val zhHK: String?
  val es: String?
}

fun I18n.localize(): String {
  return when (
    LocaleUtil.getLanguage()
      ?: LocaleUtil.getLanguageMapping()
  ) {
    LocaleConst.zhHK -> this.zhHK
    LocaleConst.zhCN -> this.zhCN
    LocaleConst.ES -> if (this.es.isNullOrEmpty()) this.enUS else this.es
    else -> this.enUS
  } ?: ""
}

@Parcelize
data class InternationalizationContent(
  @SerializedName("zh-HK")
  override val zhHK: String?,
  @SerializedName("zh-CN")
  override val zhCN: String?,
  @SerializedName("en-US")
  override val enUS: String?,
  @SerializedName("es")
  override val es: String?
) : I18n, Parcelable {

  override fun toString(): String {
    return enUS + zhCN + zhHK + es
  }
  companion object {
    fun mock(mock: String): InternationalizationContent {
      return InternationalizationContent(mock, mock, mock, mock)
    }
  }
}

@Parcelize
data class InternationalizationContents(
  @SerializedName("zh-HK")
  val zhHK: List<String>?,
  @SerializedName("zh-CN")
  val zhCN: List<String>?,
  @SerializedName("en-US")
  val enUS: List<String>?,
  @SerializedName("es")
  val es: List<String>?
) : Parcelable

fun InternationalizationContents.localize(): List<String> {
  val currentLanguage = LocaleUtil.getLanguage() ?: LocaleUtil.getResourcesLocale()
  var str: List<String>? = null
  str = when (currentLanguage) {
    "zh-HK" -> this.zhHK
    "zh-CN", "zh-Hans" -> this.zhCN
    "es" -> this.es
    else -> this.enUS
  }
  return str ?: listOf()
}

// MARK: - GlobalConfigModel
data class GlobalConfigModel(
  var name: String = "",
//  var country: String = "",
//  var currency: String = "",
//  var sign: String = "",
  var area: String = "",
  var digit: Int = 0,
  var order: Int = 0,
  var placeholder: String? = null,
) {

  constructor(map: Map<String, Any>) : this() {
    name = map["name"]?.toString() ?: ""
//    country = map["country"]?.toString() ?: ""
//    currency = map["currency"]?.toString() ?: ""
//    sign = map["sign"]?.toString() ?: ""
    area = map["area"]?.toString() ?: ""
    digit = if (map["digit"] != null) (map["digit"] as Double).toInt() else 0
    order = if (map["order"] != null) (map["order"] as Double).toInt() else 0
    placeholder = map["placeholder"]?.toString()
  }
}

@Parcelize
data class Fee(
  val factor: Double = 0.0,
  val flat: Int = 0
) : Parcelable

// // MARK: - Location
// data class Location(val lat: Double, val lng: Double) {}

// MARK: - Location
data class LatLon(val lat: Double, val lon: Double)

// MARK: - Points
@Parcelize
data class DriverPoint(
  val type: String = "",
  val coordinates: List<Double> = listOf()
) : Parcelable

@Parcelize
data class DriverTrace(
  val location: DriverPoint?,
  val polyline: String?
) : Parcelable

@Parcelize
data class Estimate(
  val avgEstimate: Int?,
  val avg: Double?,
  val min: Double?,
  val max: Double?,
  val deadline: String?,
  // / The parameters in extra are used for data analysis
  val extra: EstimateExtra?,
  val extraData: List<ExtraData>? = null,
) : Parcelable

/**
 * The parameters in extra are used for data analysis
 */
@Parcelize
data class EstimateExtra(
  // / The previously calculated avg corresponds to the new avg in the estimate
  val oldAvg: Double?,
  // / Google traffic parameters
  val trafficRate: Double?,
  // / Real-time parameter = currentEta / regionEtaHourly
  val etaRate: Double?,
  // / Current real-time eta, equivalent to tron.status.eta
  val currentEta: Double?,
  // / Historical average eta per hour by area for 24 hours
  val regionEtaHourly: Double?
) : Parcelable

@Parcelize
data class ExtraData(
  val name: String?,
  @Exclude
  var value: ExtraDataValue?
) : Parcelable

sealed class ExtraDataValue : Parcelable {
  @Parcelize
  class string(val v: String?) : ExtraDataValue()
  @Parcelize
  class number(val v: Number?) : ExtraDataValue()
  @Parcelize
  class map(val v: HashMap<String, Double>?) : ExtraDataValue()
}
