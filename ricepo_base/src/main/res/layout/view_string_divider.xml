<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="60dp">
    
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="@dimen/sw_28dp"
        android:layout_height="@dimen/sw_28dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/margin_left"
        android:id="@+id/card_icon"
        android:scaleType="fitXY"
        android:src="@drawable/ic_alipay"
        />

    <TextView
        android:id="@+id/tv_item_string"
        app:layout_constraintStart_toEndOf="@+id/card_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_item_detail"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="label"
        style="@style/ItemStyle"
        android:gravity="start"
        android:layout_marginLeft="8dp"
        app:layout_goneMarginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_item_detail"
        app:layout_constraintStart_toEndOf="@+id/card_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_item_string"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="detail"
        style="@style/SubTitleStyle"
        android:gravity="start"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginLeft="8dp"
        app:layout_goneMarginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_item_divider"
        app:layout_constraintStart_toStartOf="@+id/tv_item_string"
        app:layout_constraintEnd_toEndOf="@+id/tv_item_string"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="0dp"
        style="@style/DividerHorizontalStyle" />
    
    <ImageView
        android:id="@+id/iv_item_right_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/tv_item_string"
        android:visibility="gone"
        android:src="@drawable/ic_close"
        android:layout_width="20dp"
        android:layout_height="20dp" />


</androidx.constraintlayout.widget.ConstraintLayout>