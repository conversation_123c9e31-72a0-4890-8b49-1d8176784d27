package com.ricepo.base.inputmanager

import android.app.Activity
import android.app.Application.ActivityLifecycleCallbacks
import android.os.Bundle

//
// Created by <PERSON><PERSON> on 31/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class InputLifecycleCallbacksAdapter : ActivityLifecycleCallbacks {

  override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}

  override fun onActivityStarted(activity: Activity) {}
  override fun onActivityResumed(activity: Activity) {}
  override fun onActivityPaused(activity: Activity) {}
  override fun onActivityStopped(activity: Activity) {}

  override fun onActivitySaveInstanceState(
    activity: Activity,
    outState: Bundle
  ) {
  }

  override fun onActivityDestroyed(activity: Activity) {}
}
