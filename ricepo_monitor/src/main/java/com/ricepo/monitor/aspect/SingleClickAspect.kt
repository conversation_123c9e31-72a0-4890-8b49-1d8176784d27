package com.ricepo.monitor.aspect

import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Pointcut
import org.aspectj.lang.reflect.MethodSignature
import java.lang.reflect.Method

//
// Created by <PERSON><PERSON> on 18/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Aspect
class SingleClickAspect {

  @Pointcut("execution(@com.ricepo.monitor.aspect.SingleClick * *(..))")
  fun methodAnnotated() {
  }

  @Around("methodAnnotated()")
  @Throws(Throwable::class)
  fun aroundJoinPoint(joinPoint: ProceedingJoinPoint) {
//        var view: View? = null
//        for (arg in joinPoint.args) {
//            if (arg is View) {
//                view = arg as View
//                break
//            }
//        }
//        if (view == null) {
//            return
//        }

    var routeName: String? = null
    for (arg in joinPoint.args) {
      if (arg is String) {
        routeName = arg
        break
      }
    }

    if (routeName == null) return

    val methodSignature =
      joinPoint.signature as MethodSignature
    val method: Method = methodSignature.method
    if (!method.isAnnotationPresent(SingleClick::class.java)) {
      return
    }
    val singleClick: SingleClick = method.getAnnotation(SingleClick::class.java)
    if (!SingleClickHandle.isFastDoubleClick(routeName, singleClick.value)) {
      joinPoint.proceed()
    }
  }

  companion object {
    private const val DEFAULT_TIME_INTERVAL: Long = 5000
  }
}
