package com.ricepo.base.parser

import com.google.gson.ExclusionStrategy
import com.google.gson.FieldAttributes
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.ricepo.base.model.ExtraData
import com.ricepo.base.model.Food
import com.ricepo.base.model.OrderDelivery
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.parser.ExtraDataDeserializer
import com.ricepo.base.model.parser.FoodDeserializer
import com.ricepo.base.model.parser.FoodSerializer
import com.ricepo.base.model.parser.OrderDeliveryDeserializer
import com.ricepo.base.model.parser.OrderDeliverySerializer
import com.ricepo.base.model.parser.RestaurantDeserializer
import com.ricepo.base.model.parser.RestaurantSerializer

//
// Created by <PERSON><PERSON> on 16/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.

//
open class ParserFacade {

  companion object {
    private val strategy = object : ExclusionStrategy {
      override fun shouldSkipClass(clazz: Class<*>?): Boolean {
        return false
      }

      override fun shouldSkipField(f: FieldAttributes?): Boolean {
        return f?.getAnnotation(Exclude::class.java) != null
      }
    }

    val gsonBuilder = GsonBuilder()
      .addSerializationExclusionStrategy(strategy)
      .addDeserializationExclusionStrategy(strategy)
      .registerTypeAdapterFactory(GsonTypeAdapterFactory())

    /**
     * create gson object from Gson Builder
     */
    val gson: Gson = gsonBuilder.create()

    /**
     * configure type adapter
     * not use in the serializer or deserializer
     */
    fun buildGson(): Gson {
      val builder = gsonBuilder
      // restaurant for address and require tip deserialize
      builder.registerTypeAdapter(
        Restaurant::class.java,
        RestaurantDeserializer()
      )

      // restaurant for address and require tip deserialize
      builder.registerTypeAdapter(
        Restaurant::class.java,
        RestaurantSerializer()
      )

      // order delivery adapter
      builder.registerTypeAdapter(
        OrderDelivery::class.java,
        OrderDeliveryDeserializer()
      )
      builder.registerTypeAdapter(
        OrderDelivery::class.java,
        OrderDeliverySerializer()
      )

      // food adapter
      builder.registerTypeAdapter(
        Food::class.java,
        FoodDeserializer()
      )
      builder.registerTypeAdapter(
        Food::class.java,
        FoodSerializer()
      )

      builder.registerTypeAdapter(
        ExtraData::class.java,
        ExtraDataDeserializer()
      )

      return builder.create()
    }
  }

  fun builder(): GsonBuilder {
    return GsonBuilder()
      .addSerializationExclusionStrategy(strategy)
      .addDeserializationExclusionStrategy(strategy)
      .registerTypeAdapterFactory(GsonTypeAdapterFactory())
  }
}
