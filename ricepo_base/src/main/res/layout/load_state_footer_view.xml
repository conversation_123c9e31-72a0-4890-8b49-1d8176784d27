<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyle"
        android:indeterminateTint="@color/inputLineText"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/pull_icon_size"
        android:layout_gravity="center"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="40dp" />

    <TextView
        android:id="@+id/tv_retry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/retry"
        android:gravity="center"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible"
        style="@style/SubItemStyle"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"/>


</LinearLayout>