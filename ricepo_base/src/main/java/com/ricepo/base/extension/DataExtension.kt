package com.ricepo.base.extension

import kotlin.reflect.KClass
import kotlin.reflect.full.memberProperties
import kotlin.reflect.full.primaryConstructor

//
// Created by <PERSON><PERSON> on 7/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

fun <T : Any> T.deepCopy(): T {
  try {
    if (!this::class.isData) {
      return this
    }

    // need kotlin-reflect support
    return this::class.primaryConstructor!!.let {
      primaryConstructor ->
      primaryConstructor.parameters.map { parameter ->
        val value = (this::class as KClass<T>).memberProperties.first {
          it.name == parameter.name
        }.get(this)

        if ((parameter.type.classifier as? KClass<*>)?.isData == true) {
          parameter to value?.deepCopy()
        } else {
          parameter to value
        }
      }.toMap().let(primaryConstructor::callBy)
    }
  } catch (e: Exception) {
    e.printStackTrace()
    return this
  }
}
