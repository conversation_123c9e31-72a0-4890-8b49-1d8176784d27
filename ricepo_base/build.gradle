plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
}

//apply plugin: 'com.android.library'
//apply plugin: 'kotlin-android'
////apply plugin: 'kotlin-android-extensions'
//apply plugin: 'kotlin-parcelize'
//apply plugin: 'kotlin-kapt'



android {
    namespace 'com.ricepo.base'
    def globalConfiguration = rootProject.extensions.getByName("ext")
    compileSdk globalConfiguration["androidcompileSdk"]
//    buildToolsVersion globalConfiguration["androidBuildToolsVersion"]

    defaultConfig {
        minSdkVersion globalConfiguration["androidMinSdkVersion"]
        targetSdkVersion globalConfiguration["androidTargetSdkVersion"]

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    kotlinOptions {
        jvmTarget = javaVersion
    }

    dataBinding {
        enabled = true
    }

    viewBinding {
        enabled = true
    }

    compileOptions {
        sourceCompatibility javaVersion
        targetCompatibility javaVersion
    }

    buildFeatures {
        buildConfig = true
    }

}

kapt {
    generateStubs = true
}

dependencies {

    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation project(":ricepo_style")
    implementation project(":ricepo_tripartite")
    implementation project(":ricepo_monitor")

    implementation project(':aarlibs:coroutinespermission')
    implementation project(':aarlibs:coroutinespermission-common')

//    def baseDependencies = rootProject.ext.baseDependencies
//    def baseTestDependencies = rootProject.ext.baseTestDependencies


    implementation baseDependencies.kotlin
    implementation baseDependencies.kotlinCoroutines
    implementation baseDependencies.kotlinReflect
    implementation baseDependencies.rxKotlin
    implementation baseDependencies.rxAndroid
    implementation baseDependencies.appCompat
    implementation baseDependencies.recyclerview


    implementation baseDependencies.gson
//    implementation baseDependencies.autoSize
    implementation baseDependencies.immersionbar

    implementation baseDependencies.roomRuntime
    implementation baseDependencies.roomKtx

    implementation baseDependencies.viewpager2
    implementation baseDependencies.glide

    implementation baseDependencies.constraintLayout
//    implementation baseDependencies.coroutinesPermission

    implementation baseDependencies.lifecycleViewModelKtx
    implementation baseDependencies.lifecycleRuntimeKtx

    implementation baseDependencies.rxLifecycle
    implementation (baseDependencies.rxLifecycleComponent) {
        exclude  group: "androidx.activity", module: "activity"
    }
    implementation baseDependencies.activityKtx

    implementation baseDependencies.smartRefreshLayout

    implementation baseDependencies.threeTenAbp

    implementation baseDependencies.firebaseAnalytics

    implementation baseDependencies.paging
    implementation baseDependencies.coreKtx

    kapt baseDependencies.roomCompiler
    kapt baseDependencies.glideCompiler

    api baseDependencies.whatif
    api baseDependencies.firebase_config_ktx

    testImplementation baseTestDependencies.junit
    androidTestImplementation baseTestDependencies.extJunit
    androidTestImplementation baseTestDependencies.espressoCore

}
