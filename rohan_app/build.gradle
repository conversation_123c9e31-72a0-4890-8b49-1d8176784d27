apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'dagger.hilt.android.plugin'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
//apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: 'org.jetbrains.kotlin.plugin.parcelize'



android {
    namespace "com.ricepo.app"
    // dynamic build params
    def _versionCode = 618580
    def _versionName = '6.18.58'

    if (project.hasProperty("versionCode")) {
        _versionCode = Integer.parseInt(versionCode)
    }
    if (project.hasProperty("vesionName")) {
        _versionName = versionName
    }

    println _versionCode + " " + _versionName

    def globalConfiguration = rootProject.extensions.getByName("ext")
    compileSdk globalConfiguration["androidcompileSdk"]
//    buildToolsVersion globalConfiguration["androidBuildToolsVersion"]

    defaultConfig {
        applicationId "rocks.rice.app"
        minSdk globalConfiguration["androidMinSdkVersion"]
        targetSdk globalConfiguration["androidTargetSdkVersion"]

        versionCode _versionCode
        versionName _versionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        signConfig {
            storeFile file('ricepo.keystore')
            storePassword '52951810'
            keyAlias 'ricepo'
            keyPassword '52951810'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }
    compileOptions {
        sourceCompatibility javaVersion
        targetCompatibility javaVersion
    }
    kotlinOptions {
        jvmTarget = javaVersion
    }
    dataBinding {
        enabled = true
    }

    viewBinding {
        enabled = true
    }

    buildFeatures {
        // Enables Jetpack Compose for this module
        compose true
        buildConfig true
    }
    composeOptions {
        kotlinCompilerExtensionVersion globalConfiguration["compose_version"]
    }

    flavorDimensions "default"
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.signConfig
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.signConfig
        }
    }

    packagingOptions {
        jniLibs {
            useLegacyPackaging = false
        }
    }
    productFlavors {
//        dev {
//            // create dev project for google service
//            applicationIdSuffix '.dev'
//            versionNameSuffix '-dev'
////            buildConfigField("String", "APP_HOME", "\"dev\"")
//            manifestPlaceholders = [
//                    app_name: "@string/app_name_dev"
//            ]
//        }
//        beta {
////            buildConfigField("String", "APP_HOME", "\"beta\"")
//            manifestPlaceholders = [
//                    app_name: "@string/app_name"
//            ]
//        }
        prod {
//            buildConfigField("String", "APP_HOME", "\"prod\"")
            manifestPlaceholders = [
                    app_name_rice: "@string/app_name_rice"
            ]
        }
    }
    sourceSets {
        prod {
            androidTest {
                java.srcDirs += ['src/androidTest/java']
            }
        }
//        beta {
//            androidTest {
//                java.srcDirs += ['src/androidTest/java']
//            }
//        }
//        dev {
//            androidTestDev {
//                // other build variant still run unit test in thi folder
//                java.srcDirs += ['src/androidTestDev/java']
//            }
//        }
    }

    lintOptions {
        // if true, stop the gradle build if errors are found
        abortOnError false
        // if true, only report errors.
        ignoreWarnings true

        lintConfig file('lint.xml')
    }

    tasks.withType(JavaCompile) {
        configure(options) {
            options.compilerArgs << "-Adagger.hilt.disableModulesHaveInstallInCheck=true"
        }
    }

}

kapt {
    generateStubs = true

    arguments {
        // These no module name, at 'build.gradle', check the name is corrected
        arg("AROUTER_MODULE_NAME", project.getName())
    }
}

dependencies {

    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.aar'])


    implementation project(':ricepo_base')
    implementation project(':ricepo_network')
    implementation project(':ricepo_style')
    implementation project(':ricepo_map')
    implementation project(':ricepo_monitor')
    implementation project(':ricepo_tripartite')

    implementation project(':aarlibs:coroutinespermission')
    implementation project(':aarlibs:coroutinespermission-common')


    def appDependencies = rootProject.ext.appDependencies
    def appTestDependencies = rootProject.ext.appTestDependencies
    def common = rootProject.ext.common

    implementation appDependencies.kotlin
    implementation appDependencies.kotlinCoroutines
    testImplementation appDependencies.kotlinCoroutine_tst
    testImplementation appDependencies.turbine
    implementation appDependencies.appCompat
    implementation appDependencies.constraintLayout

    implementation appDependencies.navigationFragmentKtx
    implementation appDependencies.navigationUiKtx

    implementation appDependencies.lifecycleExtensions

    implementation appDependencies.rxAndroid
    implementation appDependencies.rxKotlin

    implementation appDependencies.javaxInject

    //hilt
    implementation appDependencies.hiltAndroid
    kapt appDependencies.hiltCompiler

    implementation appDependencies.arouter
//    implementation appDependencies.autoSize

    implementation appDependencies.material
    implementation appDependencies.immersionbar

    implementation appDependencies.roomRuntime
    implementation appDependencies.roomKtx

    implementation appDependencies.googlePlaces

    implementation appDependencies.retrofit

    implementation appDependencies.activityKtx
    implementation appDependencies.fragmentKtx
    implementation appDependencies.swipeRefreshLayout
    implementation appDependencies.viewpager2
    implementation appDependencies.threeTenAbp
    implementation appDependencies.webkit
    implementation appDependencies.security
    implementation appDependencies.phonenumber

    implementation appDependencies.rxLifecycle
    implementation (appDependencies.rxLifecycleComponent) {
        exclude  group: "androidx.activity", module: "activity"
    }

    implementation appDependencies.circleImageView
//    implementation appDependencies.coroutinesPermission

    implementation appDependencies.stripe
    implementation appDependencies.wechat

    implementation(appDependencies.paypal){
        exclude group: "org.jfrog.cardinalcommerce.gradle", module: "cardinalmobilesdk"
    }
    implementation("org.jfrog.cardinalcommerce.gradle:cardinalmobilesdk:2.2.7-2")

    implementation appDependencies.paging
    implementation appDependencies.workRuntimeKtx
    implementation appDependencies.workRxJava

    implementation appDependencies.smartRefreshLayout
    implementation appDependencies.smartRefreshClassicsHeader
    implementation appDependencies.smartRefreshClassicsFooter

    implementation appDependencies.firebaseLinksKtx
    implementation appDependencies.firebaseMessaging

    implementation appDependencies.banner

    implementation appDependencies.firebaseAnalytics

    implementation appDependencies.playServicesBase

    implementation appDependencies.gravitySnapHelper

//    implementation appDependencies.appUpdate
//    implementation appDependencies.appUpdateKtx
//    implementation appDependencies.review
    implementation appDependencies.reviewKtx

    implementation appDependencies.startup

//    implementation appDependencies.indicatorSeekbar

//    implementation appDependencies.gifDrawable
    implementation appDependencies.coilGif

    implementation appDependencies.branchIO

    kapt appDependencies.arouterCompiler
    kapt appDependencies.roomCompiler

    implementation appDependencies.leakCanaryProd
    implementation appDependencies.leakCanaryPlumber

    debugImplementation appDependencies.leakCanary

    implementation appDependencies.materialratingbar

    implementation common.compose_runtime
    // Integration with activities
    implementation common.compose_activity
    // Compose Material Design
    implementation common.compose_material
    // Animations
    implementation common.compose_animation
    // fundation
    implementation common.compose_fundation
    // Tooling support (Previews, etc.)
    implementation common.compose_ui_tooling
    // Integration with ViewModels
    implementation common.compose_viewmodel

    implementation common.compose_preview

    // UI Tests
    androidTestImplementation common.compose_ui_test

    testImplementation appTestDependencies.junit
    testImplementation appTestDependencies.mockitoKotlin
    androidTestImplementation appTestDependencies.extJunit
    androidTestImplementation appTestDependencies.espressoCore

}
