package com.ricepo.base.model.parser

import com.google.gson.JsonElement
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.ricepo.base.model.ExtraData
import com.ricepo.base.model.ExtraDataValue
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 21/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ExtraDataSerializer : JsonSerializer<ExtraData> {

  override fun serialize(src: ExtraData?, typeOfSrc: Type?, context: JsonSerializationContext?): JsonElement {

    val jsonElement = ParserFacade.gson.toJsonTree(src)

    val value = src?.value
    if (value is ExtraDataValue.map) {
      jsonElement.asJsonObject.add("value", ParserFacade.gson.toJsonTree(value.v))
    } else if (value is ExtraDataValue.string) {
      jsonElement.asJsonObject.addProperty("value", value.v)
    } else if (value is ExtraDataValue.number) {
      jsonElement.asJsonObject.addProperty("value", value.v)
    }

    return jsonElement
  }
}
