package com.ricepo.network

import android.annotation.SuppressLint
import android.content.Context
import com.ricepo.base.BuildConfig

@SuppressLint("StaticFieldLeak")
object NetworkDebugTools {
  // only used for debug (currently is chucker) don't use this context for other things
  private var context: Context? = null

  fun init(appContext: Context) {
    if (BuildConfig.DEBUG) {
      context = appContext
    }
  }

  fun getDebugContext() = context
}
