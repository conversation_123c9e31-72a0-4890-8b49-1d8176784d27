package com.ricepo.base.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

//
// Created by Thomsen on 9/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * common adapter to show list string item
 */
class BindListAdapter<V : View, T>(
  var dataSet: List<T>?,
  private val layoutId: Int,
  private val onBindViewListener: OnBindViewListener<V, T>?
) :
  RecyclerView.Adapter<BindViewHolder<V, T>>() {

  // create new views (invoked by the layout manager)
  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int):
    BindViewHolder<V, T> {
    val view = LayoutInflater.from(parent.context)
      .inflate(layoutId, parent, false) as V
    return BindViewHolder(view)
  }

  override fun onBindViewHolder(holder: BindViewHolder<V, T>, position: Int) {
    holder.bind(dataSet?.get(position), position, onBindViewListener)
  }

  override fun getItemCount() = dataSet?.size ?: 0

  fun setData(data: List<T>?) {
    dataSet = data
    notifyDataSetChanged()
  }
}

/**
 * provide a reference to the views of each data item
 */
class BindViewHolder<V : View, T>(val view: V) : RecyclerView.ViewHolder(view) {

  fun bind(value: T?, position: Int, bindViewListener: OnBindViewListener<V, T>?) {
    bindViewListener?.onBindView(view, value, position)
  }
}

/**
 * bind view of data value
 */
interface OnBindViewListener<V, T> {
  fun onBindView(view: V, value: T?, position: Int)
}
