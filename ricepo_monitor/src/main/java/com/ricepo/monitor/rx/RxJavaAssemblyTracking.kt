/*
 * Copyright 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ricepo.monitor.rx

import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Maybe
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.functions.Function
import io.reactivex.rxjava3.functions.Supplier
import io.reactivex.rxjava3.internal.fuseable.ScalarSupplier
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Utility class to enable and disable tracking of operator application (`source.map().filter()`)
 * by capturing the current stacktrace (warning: very expensive!), have it in a debug-time accessible
 * field (when walking the references in a debugger) and append it to exceptions passing by the
 * regular `onError`.
 */
class RxJavaAssemblyTracking private constructor() {
  companion object {
    /** Simply lock out concurrent state changes.  */
    val lock = AtomicBoolean()

    /**
     * Enable the assembly tracking.
     */
    fun enable() {
      if (lock.compareAndSet(false, true)) {
        RxJavaPlugins.setOnFlowableAssembly(
          Function<Flowable<Any>, Flowable<*>> { f ->
            if (f is Supplier<*>) {
              return@Function if (f is ScalarSupplier<*>) {
                FlowableOnAssemblyScalarSupplier<Any>(f)
              } else FlowableOnAssemblySupplier<Any>(f)
            }
            FlowableOnAssembly<Any>(f)
          }
        )
        RxJavaPlugins.setOnConnectableFlowableAssembly { f -> FlowableOnAssemblyConnectable(f) }
        RxJavaPlugins.setOnObservableAssembly(
          Function<Observable<Any>, Observable<*>> {
            f ->
            if (f is Supplier<*>) {
              return@Function if (f is ScalarSupplier<*>) {
                ObservableOnAssemblyScalarSupplier<Any>(f)
              } else ObservableOnAssemblySupplier<Any>(f)
            }
            ObservableOnAssembly<Any>(f)
          }
        )
        RxJavaPlugins.setOnConnectableObservableAssembly { f -> ObservableOnAssemblyConnectable(f) }
        RxJavaPlugins.setOnSingleAssembly(
          Function<Single<Any>, Single<*>> { f ->
            if (f is Supplier<*>) {
              return@Function if (f is ScalarSupplier<*>) {
                SingleOnAssemblyScalarSupplier<Any>(f)
              } else SingleOnAssemblySupplier<Any>(f)
            }
            SingleOnAssembly<Any>(f)
          }
        )
        RxJavaPlugins.setOnCompletableAssembly(
          Function { f ->
            if (f is Supplier<*>) {
              return@Function if (f is ScalarSupplier<*>) {
                CompletableOnAssemblyScalarSupplier(f)
              } else CompletableOnAssemblySupplier(f)
            }
            CompletableOnAssembly(f!!)
          }
        )
        RxJavaPlugins.setOnMaybeAssembly(
          Function<Maybe<Any>, Maybe<*>> { f ->
            if (f is Supplier<*>) {
              return@Function if (f is ScalarSupplier<*>) {
                MaybeOnAssemblyScalarSupplier<Any>(f)
              } else MaybeOnAssemblySupplier<Any>(f)
            }
            MaybeOnAssembly<Any>(f)
          }
        )
        RxJavaPlugins.setOnParallelAssembly { t -> ParallelFlowableOnAssembly(t) }
        lock.set(false)
      }
    }

    /**
     * Disable the assembly tracking.
     */
    fun disable() {
      if (lock.compareAndSet(false, true)) {
        RxJavaPlugins.setOnCompletableAssembly(null)
        RxJavaPlugins.setOnSingleAssembly(null)
        RxJavaPlugins.setOnMaybeAssembly(null)
        RxJavaPlugins.setOnObservableAssembly(null)
        RxJavaPlugins.setOnFlowableAssembly(null)
        RxJavaPlugins.setOnConnectableObservableAssembly(null)
        RxJavaPlugins.setOnConnectableFlowableAssembly(null)
        RxJavaPlugins.setOnParallelAssembly(null)
        lock.set(false)
      }
    }
  }

  /** Utility class.  */
  init {
    throw IllegalStateException("No instances!")
  }
}
