package com.ricepo.network.resource

import com.google.gson.JsonSyntaxException
import com.ricepo.base.extension.localized
import com.ricepo.monitor.MonitorFacade
import com.ricepo.network.R
import retrofit2.HttpException
import java.io.IOException
import java.lang.Exception
import java.net.SocketTimeoutException
import java.net.UnknownHostException

//
// Created by <PERSON><PERSON> on 15/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * Custom Network Error
 */
data class NetworkError(
  var code: String? = null,
  var error: String? = null,
  override var message: String? = null,
  var details: Map<String, Any>? = null,
  var status: Int? = null
) : IOException(message)

object ErrorCode {

  const val RESPONSE_BODY_EMPTY = "response-body-empty"

  const val AUTH_FAILED = "auth-failed"

  const val TWO_MANY_REQUESTS = "too-many-requests"

  const val ADDRESS_UNDELIVERABLE = "address-undeliverable"

  const val NOT_FOUND = "not-found"

  const val SUBSCRIPTION_EXIST = "subscription-exist"

  const val GROUP_IS_CLOSED_M = "the-group-is-closed"

  const val GROUP_IS_CLOSED = "The group is closed."

  /**
   * don't show bad gateway message when get group order
   */
  const val BAD_GATEWAY_MESSAGE = "HTTP 502 "

  /**
   * food item unavailable
   */
  const val FOOD_UNAVAILABLE = "food-unavailable"

  const val FOOD_OPTION_UNAVAILABLE = "option-unavailable"

  /**
   * food item not found
   */
  const val FOOD_NOT_FOUND = "food-not-found"

  const val FOOD_OPTION_NOT_FOUND = "option-not-found"
}

object ErrorMessage {
  const val NETWORK_ERROR = "network error"
}

fun Throwable.parseByNetwork(block: (code: String?) -> Unit): Exception {
  val t = this
  t.printStackTrace()

  return try {
    // http status interceptor or body observable
    var e = Exception(t.message)
    if (t is NetworkError) {
      // hooker the network error
      if (ErrorCode.AUTH_FAILED == t.code) {
        block(t.code)
      }
      e = t
      if (e.message == null) {
        e.message = t.error
      }
      throw e
    } else if (t is UnknownHostException || t is IOException || t is HttpException ||
      t.message?.contains("IOException during API request to Stripe") == true
    ) {
      // unknown host redirect message
      // stripe payment add card api not use message (unable to resolve host)
      throw NetworkException(com.ricepo.style.R.string.error_load_failed.localized(), t)
    } else if (t is SocketTimeoutException) {
      // ssl handshake timed out
      throw NetworkException(t.message ?: "request timed out", t)
    } else if (t is NoSuchElementException) {
      // empty for restaurant of delivery page
      throw t
    } else {
      if (t is JsonSyntaxException ||
        t is KotlinNullPointerException
      ) {
//        MonitorFacade.captureException(t)
      }
      throw NetworkException(com.ricepo.style.R.string.error_unknown.localized(), t)
    }
  } catch (e: Exception) {
    e
  }
}
