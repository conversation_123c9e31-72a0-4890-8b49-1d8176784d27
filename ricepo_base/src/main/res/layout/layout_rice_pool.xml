<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_rice_pool"
    android:gravity="center_vertical"
    android:layout_gravity="center_horizontal"
    android:layout_height="28dp"
    android:layout_width="wrap_content">

<!--    <FrameLayout-->
<!--        android:id="@+id/tv_top_background"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        android:background="@drawable/button_selector"-->
<!--        android:paddingTop="1dp"-->
<!--        android:paddingLeft="1dp"-->
<!--        android:paddingRight="0.5dp"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0dp" />-->

    <ImageView
        android:id="@+id/iv_pool_timer"
        android:src="@drawable/ic_timer"
        android:scaleType="fitEnd"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_width="@dimen/dip_16"
        android:layout_height="@dimen/dip_16" />

    <TextView
        android:id="@+id/tv_pool_timer"
        android:textColor="@color/ricePoolText"
        android:textSize="@dimen/font_size_h6"
        android:fontFamily="@font/font_semibold"
        android:paddingLeft="@dimen/dip_3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="00m : 00s"/>

    <TextView
        android:id="@+id/tv_pool_message"
        android:layout_marginRight="@dimen/card_side_margin"
        android:textColor="@color/ricePoolText"
        android:textSize="@dimen/font_size_h6"
        android:ellipsize="end"
        android:maxLines="1"
        android:visibility="gone"
        tools:text="@string/test_text_length"
        tools:visibility="visible"
        android:fontFamily="@font/font_semibold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</LinearLayout>