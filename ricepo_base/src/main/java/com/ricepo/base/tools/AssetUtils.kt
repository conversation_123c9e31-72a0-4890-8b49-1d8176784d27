package com.ricepo.base.tools

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ricepo.base.BaseApplication
import com.ricepo.base.model.GlobalConfigModel
import java.io.BufferedReader
import java.io.InputStreamReader
import java.lang.StringBuilder

//
// Created by <PERSON><PERSON> on 28/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
object AssetUtils {

  var countryMap: Map<String, Map<String, Any>>? = null

  // REFACTOR map to object and use firebase config
  fun getCountry(): Map<String, Map<String, Any>>? {
    return Gson().fromJson(
      getContent("global.json"),
      object : TypeToken<Map<String, Map<String, Any>>>() {}.type
    )
  }

  fun getCountryModel(): Map<String, GlobalConfigModel>? {
    return getCountry()?.map { (key, value) ->
      key to GlobalConfigModel(value)
    }?.toMap()
  }

  fun getCountry(countryCode: String? = null, prop: String? = null): Any? {

    val cc = countryCode ?: "ES"

    if (countryMap == null) {
      countryMap = getCountry()
    }

    if (prop != null) {
      return ((countryMap?.get(cc) ?: mapOf<Any, Any>()))[prop]
    }

    return countryMap?.get(cc)
  }

  fun getCurrency(countryCode: String? = null): String {
    val cc = countryCode ?: "ES"
    if (countryMap == null) {
      countryMap = getCountry()
    }

    val currency = ((countryMap?.get(cc) ?: mapOf<Any, Any>()))["currency"]
    return currency?.toString() ?: "EUR"
  }

  /**
   * explore address list content
   */
  fun getExplore(): String {
    return getContent("explore.json")
  }

  private fun getContent(fileName: String, context: Context? = null): String {

    var innerContext = context
    if (innerContext == null) {
      innerContext = BaseApplication.context
    }

    val assetManager = innerContext.assets

    var bf = BufferedReader(InputStreamReader(assetManager.open(fileName)))

    var stringBuilder = StringBuilder()

    var line: String? = bf.readLine()
    while (line != null) {
      stringBuilder.append(line)
      line = bf.readLine()
    }

    return stringBuilder.toString()
  }
}
