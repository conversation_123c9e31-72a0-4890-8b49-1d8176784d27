package com.ricepo.base.animation

import android.app.Activity
import android.widget.FrameLayout
import com.ricepo.base.lifecycle.weak.WeakRef
import com.ricepo.base.lifecycle.weak.weakReference

//
// Created by <PERSON><PERSON> on 21/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object Loading {

  private var loadingView: LoadingView? = null

  private lateinit var loadingContext: WeakRef<Activity>

  /**
   * show loading view with decorView
   * @param activity the context of loading
   */
  fun showLoading(activity: Activity?) {
    hideLoading()

    val decorView = activity?.window?.decorView ?: return
    loadingContext = activity.weakReference()

    val contentParent = decorView.findViewById<FrameLayout>(android.R.id.content)

    loadingView = LoadingView(loadingContext.get())

    // add view
    contentParent.addView(loadingView)

    loadingView?.showLoading()
  }

  /**
   * remove loading view from decorView
   * the middle of activity will destroy when the MainActivity use singleTask
   */
  fun hideLoading(isDestroy: Boolean = false) {
    if (loadingView != null) {
      if (isDestroy) {
        loadingView?.destroy()
      } else {
        loadingView?.clear()
      }

      val decorView = loadingContext.get().window?.decorView
      if (decorView != null) {
        val contentParent = decorView.findViewById<FrameLayout>(android.R.id.content)
        contentParent.removeView(loadingView)
      }

      loadingView = null
    }
  }
}
