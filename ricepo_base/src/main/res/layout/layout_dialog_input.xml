<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_dialog_title"
        android:gravity="left"
        android:layout_marginLeft="@dimen/sw_26dp"
        android:layout_marginRight="@dimen/sw_26dp"
        android:layout_marginTop="@dimen/sw_28dp"
        android:textSize="@dimen/sw_24sp"
        android:textColor="@color/mainText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_dialog_message"
        android:gravity="left"
        android:layout_marginLeft="@dimen/sw_26dp"
        android:layout_marginRight="@dimen/sw_26dp"
        android:layout_marginTop="@dimen/sw_12dp"
        style="@style/SubTitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_dialog_input"
        android:layout_marginTop="@dimen/sw_12dp"
        android:layout_marginLeft="@dimen/sw_26dp"
        android:layout_marginRight="@dimen/sw_26dp"
        android:maxLines="1"
        style="@style/EditTextBorderStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"  />

</LinearLayout>