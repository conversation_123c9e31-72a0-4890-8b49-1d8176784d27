/*
 * Copyright 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ricepo.monitor.rx

import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.internal.fuseable.ConditionalSubscriber
import io.reactivex.rxjava3.internal.fuseable.QueueFuseable
import io.reactivex.rxjava3.internal.subscribers.BasicFuseableConditionalSubscriber
import io.reactivex.rxjava3.internal.subscribers.BasicFuseableSubscriber
import org.reactivestreams.Publisher
import org.reactivestreams.Subscriber

/**
 * Wraps a Publisher and inject the assembly info.
 *
 * @param <T> the value type
</T> */
internal class FlowableOnAssembly<T : Any>(val source: Publisher<T>) :
  Flowable<T>() {
  val assembled: RxJavaAssemblyException
  override fun subscribeActual(s: Subscriber<in T>) {
    if (s is ConditionalSubscriber<*>) {
      source.subscribe(
        OnAssemblyConditionalSubscriber(
          s as ConditionalSubscriber<in T>,
          assembled
        )
      )
    } else {
      source.subscribe(OnAssemblySubscriber(s, assembled))
    }
  }

  internal class OnAssemblySubscriber<T>(
    downstream: Subscriber<in T>?,
    val assembled: RxJavaAssemblyException
  ) : BasicFuseableSubscriber<T, T>(downstream) {
    override fun onNext(t: T) {
      downstream.onNext(t)
    }

    override fun onError(t: Throwable) {
      downstream.onError(assembled.appendLast(t))
    }

    override fun requestFusion(mode: Int): Int {
      val qs = qs
      if (qs != null) {
        val m = qs.requestFusion(mode)
        sourceMode = m
        return m
      }
      return QueueFuseable.NONE
    }

    @Throws(Throwable::class)
    override fun poll(): T? {
      return qs.poll()
    }
  }

  internal class OnAssemblyConditionalSubscriber<T : Any>(
    downstream: ConditionalSubscriber<in T>?,
    val assembled: RxJavaAssemblyException
  ) : BasicFuseableConditionalSubscriber<T, T>(downstream) {
    override fun onNext(t: T) {
      downstream.onNext(t)
    }

    override fun tryOnNext(t: T): Boolean {
      return downstream.tryOnNext(t)
    }

    override fun onError(t: Throwable) {
      downstream.onError(assembled.appendLast(t))
    }

    override fun requestFusion(mode: Int): Int {
      val qs = qs
      if (qs != null) {
        val m = qs.requestFusion(mode)
        sourceMode = m
        return m
      }
      return QueueFuseable.NONE
    }

    @Throws(Throwable::class)
    override fun poll(): T? {
      return qs.poll()
    }
  }

  init {
    assembled = RxJavaAssemblyException()
  }
}
