package com.ricepo.network.adapter.rx

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ricepo.network.resource.NetworkError
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Observer
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.exceptions.CompositeException
import io.reactivex.rxjava3.exceptions.Exceptions
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import retrofit2.HttpException
import retrofit2.Response

internal class BodyObservable<T : Any>(private val upstream: Observable<Response<T>>) :
  Observable<T>() {
  override fun subscribeActual(observer: Observer<in T>) {
    upstream.subscribe(BodyObserver(observer))
  }

  private class BodyObserver<R : Any> internal constructor(observer: Observer<in R>) :
    Observer<Response<R>> {
    private val observer: Observer<in R>
    private var terminated = false
    override fun onSubscribe(disposable: Disposable) {
      observer.onSubscribe(disposable)
    }

    override fun onNext(response: Response<R>) {
      if (response.isSuccessful) {
        response.body()?.let { observer.onNext(it) }
      } else {
        terminated = true
        var t: Throwable = HttpException(response)
        // handle custom error network
        val errorString = response.errorBody()?.string()
        if (errorString != null && errorString.isNotEmpty()) {
          try {
            val ne: NetworkError = Gson().fromJson(
              errorString,
              object : TypeToken<NetworkError>() {}.type
            )
            if (ne?.code != null) {
              t = ne
            }
          } catch (e: Exception) {
            t = e
          }
        }
        try {
          observer.onError(t)
        } catch (inner: Throwable) {
          Exceptions.throwIfFatal(inner)
          RxJavaPlugins.onError(CompositeException(t, inner))
        }
      }
    }

    override fun onComplete() {
      if (!terminated) {
        observer.onComplete()
      }
    }

    override fun onError(throwable: Throwable) {
      if (!terminated) {
        observer.onError(throwable)
      } else {
        // This should never happen! onNext handles and forwards errors automatically.
        val broken: Throwable = AssertionError(
          "This should never happen! Report as a bug with the full stacktrace."
        )
        broken.initCause(throwable)
        RxJavaPlugins.onError(broken)
      }
    }

    init {
      this.observer = observer
    }
  }
}
