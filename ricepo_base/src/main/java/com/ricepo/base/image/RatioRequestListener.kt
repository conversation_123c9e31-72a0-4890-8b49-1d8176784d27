package com.ricepo.base.image

import android.graphics.drawable.Drawable
import android.widget.ImageView
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.ricepo.style.DisplayUtil

//
// Created by <PERSON><PERSON> on 9/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RatioRequestListener(private val imageView: ImageView?) : RequestListener<Any> {

  override fun onLoadFailed(
    e: GlideException?,
    model: Any?,
    target: Target<Any>?,
    isFirstResource: Boolean
  ): Boolean {
    return false
  }

  override fun onResourceReady(
    resource: Any?,
    model: Any?,
    target: Target<Any>?,
    dataSource: DataSource?,
    isFirstResource: Boolean
  ): Boolean {
    if (imageView == null || resource == null) {
      return false
    }

    if (imageView.scaleType != ImageView.ScaleType.FIT_XY) {
      imageView.scaleType = ImageView.ScaleType.FIT_XY
    }

    val width = (resource as Drawable).intrinsicWidth
    val height = resource.intrinsicHeight

    val ratio: Double = width.toDouble() / height

    if (ratio == 0.0) {
      return false
    }

    var layoutParams = imageView.layoutParams

    layoutParams.height = Math.floor(DisplayUtil.getScreenWidth() / ratio + 0.5).toInt()
    layoutParams.width = DisplayUtil.getScreenWidth()

    imageView.layoutParams = layoutParams

    return false
  }
}
