package com.ricepo.network.resource

//
// Created by <PERSON><PERSON> on 27/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
object HttpStatus {

  /**
   * network request success
   */
  const val HTTP_OK = 200

  /**
   * network created with create group order
   */
  const val HTTP_CREATED = 201

  /**
   * response no content
   */
  const val HTTP_NO_CONTENT = 204

  /**
   * authentication failed
   */
  const val HTTP_AUTH_FAILED = 401

  /**
   * url not found
   */
  const val HTTP_NOT_FOUND = 404

  /**
   * db internal server error
   */
  const val HTTP_INTERNAL_SERVER_ERROR = 500

  /**
   * bad gateway
   */
  const val HTTP_BAD_GATEWAY = 502
}
