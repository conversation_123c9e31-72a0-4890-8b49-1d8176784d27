/*
 * Copyright 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ricepo.monitor.rx

import com.ricepo.monitor.rx.CompletableOnAssembly.OnAssemblyCompletableObserver
import io.reactivex.rxjava3.core.Completable
import io.reactivex.rxjava3.core.CompletableObserver
import io.reactivex.rxjava3.core.CompletableSource
import io.reactivex.rxjava3.exceptions.Exceptions
import io.reactivex.rxjava3.functions.Supplier

/**
 * Wraps a CompletableSource and inject the assembly info.
 */
internal class CompletableOnAssemblySupplier(val source: CompletableSource) :
  Completable(), Supplier<Any> {
  val assembled: RxJavaAssemblyException
  override fun subscribeActual(observer: CompletableObserver) {
    source.subscribe(OnAssemblyCompletableObserver(observer, assembled))
  }

  @Throws(Throwable::class)
  override fun get(): Any {
    return try {
      (source as Supplier<Any>).get()
    } catch (ex: Throwable) {
      Exceptions.throwIfFatal(ex)
      throw assembled.appendLast(ex)!!
    }
  }

  init {
    assembled = RxJavaAssemblyException()
  }
}
