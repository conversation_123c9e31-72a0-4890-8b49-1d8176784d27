/*
 * Copyright 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ricepo.monitor.rx

import com.ricepo.monitor.rx.SingleOnAssembly.OnAssemblySingleObserver
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.core.SingleObserver
import io.reactivex.rxjava3.core.SingleSource
import io.reactivex.rxjava3.internal.fuseable.ScalarSupplier

/**
 * Wraps a Publisher and inject the assembly info.
 *
 * @param <T> the value type
</T> */
internal class SingleOnAssemblyScalarSupplier<T : Any>(val source: SingleSource<T>) :
  Single<T>(), ScalarSupplier<T> {
  val assembled: RxJavaAssemblyException
  override fun subscribeActual(observer: SingleObserver<in T>) {
    source.subscribe(OnAssemblySingleObserver(observer, assembled))
  }

  override fun get(): T {
    return (source as ScalarSupplier<T>).get()
  }

  init {
    assembled = RxJavaAssemblyException()
  }
}
