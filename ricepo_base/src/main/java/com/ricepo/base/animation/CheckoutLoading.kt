package com.ricepo.base.animation

import android.app.Activity
import android.util.Log
import android.widget.FrameLayout
import com.ricepo.base.lifecycle.weak.WeakRef
import com.ricepo.base.lifecycle.weak.weakReference

//
// Created by <PERSON><PERSON> on 21/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object CheckoutLoading {

  private var loadingView: CheckoutLoadingView? = null

  private lateinit var loadingContext: WeakRef<Activity>

  private var isAnimEnd = false

  /**
   * show loading view with decorView
   * @param activity the context of loading
   */
  fun showLoading(
    activity: Activity?,
    msgId: Int = 0,
    middleMsgId: Int = 0,
    onStripeSuccess: () -> Unit
  ) {
    hideLoading()

    val decorView = activity?.window?.decorView ?: return
    loadingContext = activity.weakReference()

    val contentParent = decorView.findViewById<FrameLayout>(android.R.id.content)

//        loadingView = CheckoutLoadingView(BaseApplication.context)
    loadingView = CheckoutLoadingView(loadingContext.get())

    // add view
    contentParent.addView(loadingView)
    loadingView?.setText(msgId)

    isAnimEnd = false
    loadingView?.onOutterAnimEnd = {
      if (!isAnimEnd) {
        isAnimEnd = true
        if (msgId != 0) {
          loadingView?.setText(msgId)
        }

        loadingView?.checkoutSuccess(onStripeSuccess)
      }
    }
    loadingView?.start()
  }

  /**
   * change the animation for payment success
   */
  fun afterStripeSuccess(msgId: Int = 0, success: () -> Unit = {}) {
    Log.i("thom", "after stripe")

    if (loadingView == null) {
      // backup to end
      success()
    } else {
      loadingView?.setRepeat(false)
    }

//        // delay millis for show once of checkout tick
//        Handler().postDelayed({
//            success()
//        }, 1600)
  }

  /**
   * remove loading view from decorView
   * the middle of activity will destroy when the MainActivity use singleTask
   */
  fun hideLoading() {
    if (loadingView != null) {
      val decorView = loadingContext.get().window?.decorView
      if (decorView != null) {
        val contentParent = decorView.findViewById<FrameLayout>(android.R.id.content)
        contentParent.removeView(loadingView)

        loadingView?.quit()
      }
      loadingView = null
    }
  }
}
