<?xml version="1.0" encoding="utf-8"?>


<androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

    <LinearLayout
            android:gravity="center_vertical"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            tools:ignore="MissingConstraints">

        <ImageView
                android:id="@+id/iv_pool_timer"
                app:layout_goneMarginBottom="@dimen/dip_5"
                android:src="@drawable/ic_timer"
                android:scaleType="fitEnd"
                android:layout_marginLeft="16dp"
                android:layout_width="@dimen/dip_16"
                android:layout_height="@dimen/dip_16"/>

        <TextView
                android:id="@+id/tv_pool_timer"
                android:textColor="@color/ricePoolText"
                android:textSize="@dimen/font_size_h6"
                android:fontFamily="@font/font_semibold"
                android:paddingLeft="@dimen/dip_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="00m : 00s"/>

        <TextView
                android:id="@+id/tv_pool_message"
                android:layout_marginRight="@dimen/card_side_margin"
                android:textColor="@color/ricePoolText"
                android:textSize="@dimen/font_size_h6"
                android:ellipsize="end"
                android:maxLines="1"
                android:visibility="gone"
                tools:text="@string/test_text_length"
                tools:visibility="visible"
                android:fontFamily="@font/font_semibold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>