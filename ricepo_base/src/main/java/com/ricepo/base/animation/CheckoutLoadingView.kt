package com.ricepo.base.animation

import android.content.Context
import android.graphics.Bitmap
import android.util.AttributeSet
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ricepo.base.BaseApplication
import com.ricepo.base.R
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.DrawableAnimationView

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class CheckoutLoadingView : ConstraintLayout {

  constructor(context: Context) : super(context) {
    init(context, null)
  }

  constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
    init(context, attrs)
  }

  constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(context, attrs)
    }

  private lateinit var loadingView: View

  private var drawableAnimationView: DrawableAnimationView? = null

  var onOutterAnimEnd: () -> Unit = {}

  private fun init(context: Context?, attrs: AttributeSet?) {
    // application context not use app theme
    loadingView = LayoutInflater.from(context ?: BaseApplication.context)
      .inflate(R.layout.frame_drawable_loading, this)
    drawableAnimationView = loadingView.findViewById(R.id.v_drawable_loading)
    drawableAnimationView?.setData(getCheckoutData())
    drawableAnimationView?.isRepeat = true
    // real: 24 fps is 41 delay time
    drawableAnimationView?.mAnimTime = 20
    drawableAnimationView?.setGravity(Gravity.FILL)
    drawableAnimationView?.setAnimCallBack(object : DrawableAnimationView.AnimCallBack {
      override fun onAnimChange(position: Int, bitmap: Bitmap?) {
      }

      override fun onAnimEnd() {
        Log.i("thom", "on anim end")
        onOutterAnimEnd()
      }
    })
  }

  fun setRepeat(isRepeat: Boolean) {
    drawableAnimationView?.isRepeat = isRepeat
  }

  private fun getCheckoutData(): ArrayList<DrawableAnimationView.AnimData>? {
    val datas: ArrayList<DrawableAnimationView.AnimData> = ArrayList()
    var data: DrawableAnimationView.AnimData
    var resId: Int
    var fileName = "checkout_loading"
    var suffixValue = ""
    for (i in 0..79) {
      if (i < 10) {
        suffixValue += "00"
      } else if (i < 100) {
        suffixValue += "0"
      }
      resId = ResourcesUtil.getDrawableId("${fileName}_00$suffixValue$i")
      if (resId > 0) {
        data = DrawableAnimationView.AnimData()
        data.filePath = resId
        datas.add(data)
      }
      suffixValue = ""
    }
    return datas
  }

  private fun getTickData(): ArrayList<DrawableAnimationView.AnimData>? {
    val datas: ArrayList<DrawableAnimationView.AnimData> = ArrayList()
    var data: DrawableAnimationView.AnimData
    var resId: Int
    var fileName = "checkout_tick"
    var suffixValue = ""
    for (i in 1..49) {
      if (i < 10) {
        suffixValue += "00"
      } else if (i < 100) {
        suffixValue += "0"
      }
      resId = ResourcesUtil.getDrawableId("${fileName}_00$suffixValue$i")
      if (resId > 0) {
        data = DrawableAnimationView.AnimData()
        data.filePath = resId
        datas.add(data)
      }
      suffixValue = ""
    }
    return datas
  }

  fun setText(message: String?) {
    if (!message.isNullOrEmpty()) {
      loadingView.findViewById<TextView>(R.id.tv_loading)?.text = message
    }
  }

  fun setText(msgId: Int = 0) {
    if (msgId != 0) {
      setText(ResourcesUtil.getString(msgId))
    }
  }

  fun checkoutSuccess(success: () -> Unit = {}) {
    pause()
    drawableAnimationView?.clearData()
    drawableAnimationView?.setData(getTickData())
    drawableAnimationView?.isRepeat = false
    drawableAnimationView?.setAnimCallBack(object : DrawableAnimationView.AnimCallBack {
      override fun onAnimChange(position: Int, bitmap: Bitmap?) {
      }

      override fun onAnimEnd() {
        success()
      }
    })
    start()
  }

  fun start() {
    drawableAnimationView?.start()
  }

  fun resume() {
    drawableAnimationView?.resume()
  }

  fun pause() {
    drawableAnimationView?.pause()
  }

  /**
   * only remove and hide view
   */
  fun quit() {
    drawableAnimationView?.quit()
    drawableAnimationView = null
  }
}
