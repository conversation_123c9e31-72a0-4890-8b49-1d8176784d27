package com.ricepo.monitor.firebase

import com.google.firebase.analytics.ktx.ParametersBuilder

//
// Created by <PERSON><PERSON> on 16/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object FirebaseEventName {
  // base
  const val rExitApp = "r_exit_app"
  const val rLeavePage = "r_leave_page"

  // restaurant
  const val rScrollHorizontal = "r_scroll_horizontal"
  const val rSelectRestaurant = "r_select_restaurant"
  const val rShowMore = "r_show_more"

  // menu
  const val rAddItem = "r_add_item"
  const val rRemoveItem = "r_remove_item"
  const val rSearchMenu = "r_search_menu"
  const val rMenuSelectTab = "r_menu_select_tab"

  // profile / order
  const val rRefer = "r_refer"
  const val rReorder = "r_reorder"
  const val rGetReceipt = "r_get_receipt"

  // pool
  const val rPoolView = "r_pool_view"
  const val rPoolCheckout = "r_pool_checkout"
  const val rPoolOrder = "r_pool_order"

  // lucky
  const val rLuckyAdd = "r_lucky_add"
  const val rLuckyRemove = "r_lucky_remove"
  const val rLuckyRefresh = "r_lucky_refresh"
  const val rLuckyCheckout = "r_lucky_checkout"

  const val rLuckySwipe = "r_restaurant_lucky_swipe"
  const val rLuckySubmit = "r_restaurant_lucky_submit"

  // checkout
  const val rPlaceOrder = "r_place_order"

  // recommend more event
  const val rMatchShow = "r_match_show"
  const val rMatchScroll = "r_match_scroll"
  const val rMatchAdd = "r_match_add"
  const val rMatchClose = "r_match_close"

  // cascade event
  const val rCascadeSelect = "r_cascade_select"
  const val rCascadeScroll = "r_cascade_scroll"
  const val rCascadeMore = "r_cascade_more"
}

/**
 * the firebase event log object
 */
sealed class FirebaseEvent() {
  open fun addParams(
    builder: ParametersBuilder,
    key: String,
    value: Any?
  ): ParametersBuilder {
    value?.let {
      when (it) {
        is String -> builder.param(key, it)
        is Long -> builder.param(key, it)
        is Double -> builder.param(key, it)
        is Int -> builder.param(key, it.toLong())
      }
    }
    return builder
  }

  abstract fun toParams(): ParametersBuilder

  abstract var rScrollDepth: Double?

  fun rScrollDepth() {
    rScrollDepth?.let {
      if (it < 0.0) {
        rScrollDepth = 0.0
      }
    }
    rScrollDepth
  }
}

data class FirebaseBaseEvent(
  var rSource: String? = null,
  override var rScrollDepth: Double? = null,
) : FirebaseEvent() {

  override fun toParams(): ParametersBuilder {
    val builder = ParametersBuilder()
    addParams(builder, "r_source", rSource)
    addParams(builder, "r_scroll_depth", rScrollDepth())
    return builder
  }
}

data class FirebaseRestaurantEvent(
  var rGroupType: String? = null,
  var rGroupId: String? = null,
  var rGroupIndex: Long? = null,
  var rRestaurantIndex: Long? = null,
  var rOnFood: Boolean = false,
  override var rScrollDepth: Double? = null,
  var rSource: String? = null,
  var rPoolTime: Long? = null,
) : FirebaseEvent() {

  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    addParams(builder, "r_group_type", rGroupType)
    addParams(builder, "r_group_id", rGroupId)
    addParams(builder, "r_group_index", rGroupIndex)
    addParams(builder, "r_restaurant_index", rRestaurantIndex)
    addParams(builder, "r_scroll_depth", rScrollDepth())
    addParams(builder, "r_source", rSource)
    addParams(builder, "r_pool_time", rPoolTime)
    addParams(builder, "r_on_food", rOnFood)
    return builder
  }
}

data class FirebaseMenuEvent(
  var rCategoryType: String? = null,
  var rCategoryId: String? = null,
  var rCategoryIndex: Long? = null,
  var rFoodIndex: Long? = null,
  override var rScrollDepth: Double? = null,
  var rSource: String? = null,
) : FirebaseEvent() {

  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    addParams(builder, "r_category_type", rCategoryType)
    addParams(builder, "r_category_id", rCategoryId)
    addParams(builder, "r_category_index", rCategoryIndex)
    addParams(builder, "r_food_index", rFoodIndex)
    addParams(builder, "r_scroll_depth", rScrollDepth())
    addParams(builder, "r_source", rSource)
    return builder
  }
}

data class FirebaseMenuSearchEvent(
  var rKeyword: String? = null,
  override var rScrollDepth: Double? = null,
) : FirebaseEvent() {

  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    addParams(builder, "r_keyword", rKeyword)
    return builder
  }
}

data class FirebaseReferEvent(
  var rMethod: String? = null,
  override var rScrollDepth: Double? = null,
) : FirebaseEvent() {

  companion object {
    const val WECHAT = "wechat"
    const val WECHAT_MOMENT = "wechat-moment"
  }

  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    addParams(builder, "r_method", rMethod)
    return builder
  }
}

data class FirebasePoolEvent(
  val rPoolTime: Long? = null,
  override var rScrollDepth: Double? = null,
) : FirebaseEvent() {

  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    addParams(builder, "r_pool_time", rPoolTime)
    return builder
  }
}

data class FirebaseLuckyEvent(
  val rLuckyRestSelected: Boolean? = null,
  override var rScrollDepth: Double? = null,
) : FirebaseEvent() {

  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    rLuckyRestSelected?.let {
      addParams(builder, "r_lucky_rest_selected", if (it) "yes" else "no")
    }
    return builder
  }
}

data class FirebaseMenuSelectEvent(
  val rMenuNavType: String? = null,
  val rMenuNavIndex: Int? = null,
  override var rScrollDepth: Double? = null,
) : FirebaseEvent() {

  companion object {
    const val TYPE_SIDE = "side"
    const val TYPE_HEADER = "header"
  }

  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    addParams(builder, "r_menu_nav_type", rMenuNavType)
    addParams(builder, "r_menu_nav_index", rMenuNavIndex)
    return builder
  }
}

data class FirebaseLuckCombineEvent(
  val rRestaurantLuckyIndex: Int? = null,
  override var rScrollDepth: Double? = null,
) : FirebaseEvent() {

  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    addParams(builder, "r_restaurant_lucky_index", rRestaurantLuckyIndex)
    return builder
  }
}

data class FirebaseRecommendMenuEvent(
  var rCartCount: Int?,
  override var rScrollDepth: Double? = null,
) : FirebaseEvent() {
  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    addParams(builder, "r_cart_count", rCartCount)
    addParams(builder, "r_scroll_depth", rScrollDepth())
    return builder
  }
}

data class FirebaseCascadeEvent(
  var rPrice: Int? = null,
  override var rScrollDepth: Double? = null,
) : FirebaseEvent() {
  override fun toParams(): ParametersBuilder {
    var builder = ParametersBuilder()
    addParams(builder, "r_price", rPrice)
    addParams(builder, "r_scroll_depth", rScrollDepth())
    return builder
  }
}
