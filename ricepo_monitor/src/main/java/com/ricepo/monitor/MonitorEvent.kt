package com.ricepo.monitor

//
// Created by <PERSON><PERSON> on 15/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

data class MonitorEvent(
  val message: String,
  var extras: Map<String, Any>? = null,
  var environment: String? = null,
  var level: EventLevel? = null
)

enum class EventLevel {
  LOG,
  DEBUG,
  INFO,
  WARNING,
  ERROR,
  FATAL,
}

object EventLabel {
  const val CAUSE = "cause"
}
