package com.ricepo.base.lifecycle

import android.view.View
import androidx.core.view.doOnDetach
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

private val viewCoroutineExceptionHandler = CoroutineExceptionHandler { _, e ->
  println("viewCoroutineException: ${e.message}")
}

fun View.launch(
  block: suspend () -> Unit
) {
  val job = CoroutineScope(
    Dispatchers.Main + viewCoroutineExceptionHandler
  ).launch {
    block.invoke()
  }
  doOnDetach {
    job.cancel()
  }
}

private val viewScopeMap = ConcurrentHashMap<View, CoroutineScope>()
val View.viewScope: CoroutineScope
  get() {
    doOnDetach {
      viewScopeMap[this]?.cancel()
      viewScopeMap.remove(this)
    }
    return viewScopeMap[this] ?: CoroutineScope(
      SupervisorJob() + Dispatchers.Main.immediate
    ).let {
      viewScopeMap[this] = it
      it
    }
  }
