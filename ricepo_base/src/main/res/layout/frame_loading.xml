<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@android:color/transparent"
    android:focusableInTouchMode="true"
    android:clickable="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<!--    <ImageView-->
<!--        android:id="@+id/iv_loading"-->
<!--        android:layout_width="@dimen/loading_size"-->
<!--        android:layout_height="@dimen/loading_size"-->
<!--        android:layout_gravity="center" />-->

    <com.ricepo.style.view.DrawableAnimationView
        android:id="@+id/iv_loading"
        android:layout_width="@dimen/loading_size"
        android:layout_height="@dimen/loading_size"
        android:layout_gravity="center" />

</FrameLayout>