package com.ricepo.monitor.log

import android.content.Context
import android.os.Environment
import android.util.Log
import com.ricepo.monitor.BuildConfig
import java.io.File
import java.lang.Exception
import java.text.SimpleDateFormat
import java.util.Date

//
// Created by <PERSON><PERSON> on 5/13/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
object Logger {

  fun init(context: Context) {
    try {
      initXlog(context)
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  fun flush() {
    try {
//      Log.appenderFlush()
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  fun close() {
    try {
//      Log.appenderClose()
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  fun d(tag: String, content: String) {
    Log.d(tag, content)
  }

  fun i(tag: String, content: String) {
    Log.i(tag, content)
  }

  fun w(tag: String, content: String) {
    Log.w(tag, content)
  }

  fun w(tag: String, content: String, e: Throwable) {
    Log.w(tag, content + "\n" + e.stackTraceToString())
  }

  fun getLogFile(context: Context): File? {
    var logFile = File(
      context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS),
      "xlog/ricepo_${SimpleDateFormat("yyyyMMdd").format(Date())}.xlog"
    )
    if (!logFile.exists()) {
      logFile = File(
        context.externalCacheDir,
        "xlog/ricepo_${SimpleDateFormat("yyyyMMdd").format(Date())}.xlog"
      )
    }
    return logFile
  }

  private fun initXlog(context: Context) {
    val dirFile = File(
      context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS),
      "xlog"
    )
    if (!dirFile.exists()) {
      dirFile.mkdir()
    }
    val logPath = dirFile.absolutePath
    val logFileName = "ricepo"

//    val cachePath = File(context.externalCacheDir, "xlog").absolutePath
//
//    // init xlog
//    val logConfig = Xlog()
//    Log.setLogImp(logConfig)
//
//    Log.setConsoleLogOpen(BuildConfig.DEBUG)
//    if (BuildConfig.DEBUG) {
//      Log.appenderOpen(
//        Xlog.LEVEL_DEBUG, Xlog.AppednerModeAsync, cachePath,
//        logPath, logFileName, 3
//      )
//    } else {
//      Log.appenderOpen(
//        Xlog.LEVEL_INFO, Xlog.AppednerModeAsync, cachePath,
//        logPath, logFileName, 1
//      )
//    }
  }

  init {
//    System.loadLibrary("c++_shared")
//    System.loadLibrary("marsxlog")
  }
}
