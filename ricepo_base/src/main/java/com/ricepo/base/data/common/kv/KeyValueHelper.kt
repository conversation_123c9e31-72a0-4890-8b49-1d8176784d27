package com.ricepo.base.data.common.kv

import com.ricepo.base.BaseApplication
import com.ricepo.base.data.common.CommonDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 5/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
open class KeyValueHelper {
  /**
   * provide key value dao
   */
  private fun dataSource(): KeyValueDao {
    val database =
      CommonDatabase.getInstance(BaseApplication.context)
    return database.keyValueDao()
  }

  protected suspend fun saveValue(key: String, data: Any, isUpdate: Boolean = true) {
    dataSource().saveValue(key, data, isUpdate)
  }

  protected suspend fun deleteValue(key: String): Int {
    return dataSource().deleteValue(key)
  }

  protected fun getValue(key: String, apply: (String?) -> Unit) {
    getValue(listOf(key), apply)
  }

  protected fun getValue(key: String): String? {
    return dataSource().getValue(listOf(key))
  }

  protected suspend fun getValueSuspend(key: String): String? {
    return dataSource().getValueSuspend(listOf(key))
  }

  protected fun getValue(keys: List<String>, apply: (String?) -> Unit) {
    GlobalScope.launch(Dispatchers.IO) {
      val value = dataSource().getValue(keys)

      withContext(Dispatchers.Main) {
        try {
          apply(value)
        } catch (e: Exception) {
          e.printStackTrace()
        }
      }
    }
  }

  protected fun getValue(keys: List<String>): String? {
    return dataSource().getValue(keys)
  }

  protected suspend fun getValueSuspend(keys: List<String>): String? {
    return dataSource().getValueSuspend(keys)
  }

  protected fun query(io: () -> String, apply: (String) -> Unit) {
    GlobalScope.launch(Dispatchers.IO) {
      val value = io()

      withContext(Dispatchers.Main) {
        apply(value)
      }
    }
  }
}
