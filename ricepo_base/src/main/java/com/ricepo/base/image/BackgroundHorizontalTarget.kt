package com.ricepo.base.image

import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.drawable.Drawable
import android.widget.ImageView
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.google.gson.Gson
import com.ricepo.base.tools.ActivityUtils
import com.ricepo.style.DisplayUtil

//
// Created by <PERSON><PERSON> on 06/08/2021.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * the gallery background crop target
 */
class BackgroundHorizontalTarget(private val imageView: ImageView) :
  CustomTarget<Bitmap>() {
//    CustomViewTarget<ImageView, Bitmap>(imageView) {

  companion object {
    private var backViewWidth: Int = 0
  }

  override fun onLoadFailed(errorDrawable: Drawable?) {
  }

  override fun onLoadCleared(placeholder: Drawable?) {
    backViewWidth = 0
    builder.clear()
  }

  private val builder = StringBuilder()

//    override fun onResourceCleared(placeholder: Drawable?) {
//        backViewWidth = 0
//    }

  override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {

    var viewWidth = imageView.width
    val viewHeight = imageView.height

    if (viewWidth != 0) else return

    val imageWidth = resource.width
    val imageHeight = resource.height

    val gson = Gson().toJson(imageView.resources.displayMetrics)
    builder.append(gson)

    // into CustomTarget get the bitmap real size
    // CustomViewTarget get the bitmap height with imageView common
    val msg1 = "imageWidth = $imageWidth imageHeight = $imageHeight"
    val msg2 = "viewWidth = $viewWidth viewHeight = $viewHeight"
//        Log.i("thom", msg1)
//        Log.i("thom", msg2)
    builder.append(msg1)
    builder.append(msg2)

    if (imageWidth > viewWidth) {
      backViewWidth = viewWidth
//            val scaleBitmap = scaleBitmap(resource, viewWidth, viewHeight / viewWidth)
      val offset = DisplayUtil.getScreenWidth().times(0).toInt()
      viewWidth += offset

      val scaleBitmap = scaleBitmap(resource, viewWidth, viewHeight)
      val cropBitmap = cropBitmap(
        scaleBitmap,
        viewWidth,
        viewHeight
      )
      val bitmap = cropBitmap
      if (!ActivityUtils.isActivityDestroy(imageView.context)) {
        if (bitmap?.isRecycled == true) {
          imageView.setImageDrawable(null)
        } else {
          imageView.setImageBitmap(bitmap)
        }
      }
      // popup debug for display metrics
//            DialogFacade.showAlert(imageView.context, builder.toString())
      return
    }

    if (!ActivityUtils.isActivityDestroy(imageView.context)) {
      if (resource.isRecycled) {
        imageView.setImageDrawable(null)
      } else {
        imageView.setImageBitmap(resource)
      }
    }
  }

  private fun scaleBitmap(origin: Bitmap?, newWidth: Int, newHeight: Int): Bitmap? {
    val origin = origin ?: return null
    val width = origin.width
    val height = origin.height

    try {
      val scaleWidth = newWidth.toFloat() / width
      val scaleHeight = newHeight.toFloat() / height
//        val scaleRatio = Math.min(scaleWidth, scaleHeight)
      val matrix = Matrix()
      matrix.preScale(scaleHeight, scaleHeight)
      // width and height must be > 0
      val bitmap = Bitmap.createBitmap(origin, 0, 0, width, height, matrix, false)
//        if (origin.equals(bitmap)) {
//            return bitmap
//        }
//        origin.recycle()
      val msg3 = "imageScaleWidth = ${bitmap?.width} imageScaleHeight = ${bitmap?.height}"
//        Logger.i("thom", msg3)
      builder.append(msg3)
      return bitmap
    } catch (e: java.lang.Exception) {
      e.printStackTrace()
    }

    return null
  }

  private fun cropBitmap(bitmap: Bitmap?, width: Int, height: Int): Bitmap? {
    return try {
      if (bitmap != null) {
        val originWidth = bitmap.width
        val originHeight = bitmap.height
        // height int to 1
        val ratio = height.toFloat() / originHeight
        val targetHeight = height
        val offset = DisplayUtil.dp2PxOffset(125f)
        val targetWidth = (ratio * (width)).toInt()
        val target = Bitmap.createBitmap(
          bitmap, 0, 0, targetWidth, targetHeight
        )
        val msg4 = "imageCropWidth = ${target?.width} imageCropHeight = ${target?.height}"
//                Logger.i("thom", msg4)
        builder.append(msg4)
        target
      } else {
        bitmap
      }
    } catch (ex: Exception) {
      ex.printStackTrace()
      bitmap
    }
  }
}
