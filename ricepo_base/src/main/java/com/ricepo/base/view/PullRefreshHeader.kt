package com.ricepo.base.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.annotation.ColorInt
import com.ricepo.base.R
import com.ricepo.base.image.ImageLoader
import com.ricepo.style.ResourcesUtil
import com.scwang.smart.refresh.layout.api.RefreshHeader
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.constant.SpinnerStyle
import com.scwang.smart.refresh.layout.util.SmartUtil

//
// Created by <PERSON><PERSON> on 27/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class PullRefreshHeader : LinearLayout, RefreshHeader {

  private lateinit var mProgressView: ImageView

  constructor(context: Context) : super(context) {
    initView(context)
  }

  constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
    initView(context)
  }

  constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
    context,
    attrs,
    defStyleAttr
  ) {
    initView(context)
  }

  private fun initView(context: Context) {
    gravity = Gravity.CENTER
    mProgressView = ImageView(context)
    addView(
      mProgressView, ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_40dp),
      ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_40dp)
    )
    minimumHeight = SmartUtil.dp2px(60f)
  }

  private fun startProcess() {
    ImageLoader.load(mProgressView, com.ricepo.style.R.drawable.loading)
  }

  private fun stopProcess() {
    val view = mProgressView
    ImageLoader.with(view)?.clear(view)
  }

  override fun getView(): View {
    return this
  }

  override fun getSpinnerStyle(): SpinnerStyle {
    return SpinnerStyle.FixedBehind
  }

  @SuppressLint("RestrictedApi")
  override fun onStartAnimator(layout: RefreshLayout, headHeight: Int, maxDragHeight: Int) {
    startProcess()
  }

  @SuppressLint("RestrictedApi")
  override fun onFinish(layout: RefreshLayout, success: Boolean): Int {
    stopProcess()
    return 100
  }

  @SuppressLint("RestrictedApi")
  override fun onStateChanged(
    refreshLayout: RefreshLayout,
    oldState: RefreshState,
    newState: RefreshState
  ) {
    val processView = mProgressView ?: return
    when (newState) {
      RefreshState.None -> {
        processView.visibility = GONE
        stopProcess()
      }
      RefreshState.PullDownToRefresh,
      RefreshState.Refreshing -> {
        processView.visibility = VISIBLE
        startProcess()
      }
      RefreshState.ReleaseToRefresh -> {
      }
      else -> {}
    }
  }

  override fun isSupportHorizontalDrag(): Boolean {
    return false
  }

  override fun autoOpen(duration: Int, dragRate: Float, animationOnly: Boolean): Boolean {
    return false
  }

  @SuppressLint("RestrictedApi")
  override fun onInitialized(kernel: RefreshKernel, height: Int, maxDragHeight: Int) {}

  @SuppressLint("RestrictedApi")
  override fun onMoving(
    isDragging: Boolean,
    percent: Float,
    offset: Int,
    height: Int,
    maxDragHeight: Int
  ) {}

  @SuppressLint("RestrictedApi")
  override fun onReleased(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {}

  @SuppressLint("RestrictedApi")
  override fun onHorizontalDrag(percentX: Float, offsetX: Int, offsetMax: Int) {}

  @SuppressLint("RestrictedApi")
  override fun setPrimaryColors(@ColorInt vararg colors: Int) {}
}
