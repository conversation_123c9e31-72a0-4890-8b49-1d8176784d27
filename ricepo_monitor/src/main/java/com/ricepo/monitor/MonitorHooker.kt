package com.ricepo.monitor

import android.util.Log
import com.ricepo.monitor.log.Logger
import com.ricepo.monitor.rx.ObservableOnAssembly
import com.ricepo.monitor.rx.RxJavaAssemblyTracking
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Observer
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.exceptions.UndeliverableException
import io.reactivex.rxjava3.functions.Consumer
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import java.io.IOException
import java.net.ConnectException
import java.net.SocketException
import java.net.SocketTimeoutException

//
// Created by <PERSON><PERSON> on 20/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MonitorHooker {

  companion object {
    /**
     * init global rx error handler
     */
    fun initRxHooker(isDebug: <PERSON>olean, handler: MonitorHandler? = null) {
      RxJavaPlugins.setOnObservableSubscribe { _, t2 ->
        ObservableSubscribeHooker(t2, isDebug, handler)
      }
      RxJavaPlugins.setErrorHandler(ErrorHandlerHooker(isDebug))

      // tracking via
      if (isDebug) {
        RxJavaAssemblyTracking.enable()
      } else {
        RxJavaAssemblyTracking.disable()
      }
    }
  }
}

interface MonitorHandler {
  fun error(t: Throwable): Exception
}

/**
 * default error handler
 */
class ErrorHandlerHooker<T : Any>(private var isDebug: Boolean) : Consumer<T> {

  private val TAG = "ErrorHandlerHooker"

  override fun accept(t: T) {
    val e = t
    if (e is UndeliverableException) {
//            e = e.getCause()
    }
    if ((e is IOException) || (e is SocketException)) {
      // fine, irrelevant network problem or API that throws on cancellation
    }
    if (e is InterruptedException) {
      // fine, some blocking code was interrupted by a dispose call
    }
    if ((e is NullPointerException) || (e is IllegalArgumentException)) {
      // that's likely a bug in the application
      captureByMonitor(e as Exception)
    }
    if (e is IllegalStateException) {
      // that's a bug in RxJava or in a custom operator
      captureByMonitor(e)
    }
    (e as? Exception)?.run {
      Log.w(TAG, this)
      printStackTrace()
    }
    Observable.empty<T>()
  }

  // send event to monitor
  private fun captureByMonitor(e: Exception) {
    try {
      val message = checkNotNull(e.localizedMessage) {
        e.message ?: "observable error"
      }
      val event = MonitorEvent(message)
      event.level = if (isDebug) EventLevel.DEBUG else EventLevel.ERROR
      event.environment = BuildConfig.BUILD_TYPE
      event.extras = mapOf(
        "error" to "Unhandled error happened: \n $message",
        "serializedCallStack" to "subscription called from: \n ${e.stackTrace}"
      )
//      MonitorFacade.captureEvent(event)
    } catch (ex: Exception) {
      ex.printStackTrace()
    }
  }
}

/**
 * actual maybe memory leak
 */
class ObservableSubscribeHooker<T : Any>(
  private var actual: Observer<T>,
  private var isDebug: Boolean,
  private var handler: MonitorHandler?
) : Observer<T> {

  private val TAG = "ObservableHooker"

  override fun onComplete() {
    actual.onComplete()
  }

  override fun onSubscribe(d: Disposable) {
    actual.onSubscribe(d)
  }

  override fun onNext(t: T) {
    hookOnNext(t)
    actual.onNext(t)
  }

  private fun hookOnNext(t: T) {
    if (isDebug) {
      if (t is ObservableOnAssembly<*>) {
        Log.e(TAG, "${this.actual.javaClass} onNext")
        Logger.w(TAG, t.assembled.stacktrace)
      }
    }
  }

  override fun onError(e: Throwable) {
    if (isDebug) {
      Log.i(TAG, "${this.actual.javaClass} onError")
      e.printStackTrace()
    }

    if (e is ConnectException) {
      // network connect error
    }

    if (e is SocketTimeoutException) {
      // data handle error
    }

    // redirect exception
    val ve = handler?.error(e)


    // transmission error
    ve?.let { actual.onError(it) }
  }

  // send event to monitor
  private fun captureByMonitor(e: Throwable) {
    try {
      val message = checkNotNull(e.localizedMessage) {
        e.message ?: "observable error"
      }
      val event = MonitorEvent(message)
      event.level = if (isDebug) EventLevel.DEBUG else EventLevel.ERROR
      event.environment = BuildConfig.BUILD_TYPE
      event.extras = mapOf(
        "error" to "Unhandled error happened: \n $message",
        "serializedCallStack" to "subscription called from: \n ${e.stackTrace}"
      )
//      MonitorFacade.captureEvent(event)
    } catch (ex: Exception) {
      ex.printStackTrace()
    }
  }

}
