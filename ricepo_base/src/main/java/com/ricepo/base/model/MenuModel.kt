package com.ricepo.base.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.base.parser.Exclude
import com.ricepo.style.button.HamburgerButton
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 27/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

// data class MenuCategory(
//    @SerializedName("_id")
//    val id: String,
//    val name: InternationalizationContent? = null,
//    val description: InternationalizationContent? = null,
//    val items: List<Food>? = null,
//    val type: String? = null,
//    val groupId: String? = null,
//    val expand: Int?,
//    val limit: Int?,
//    val maxItem: Int?,
//    val image: ThemeImage?,
//    val categoryId: String? = null
// )

@Parcelize
data class MenuBundle(
  @SerializedName("_id")
  val id: String?,
  val name: InternationalizationContent? = null,
  val sections: List<Int>? = null,
) : Parcelable

/**
 * remote section category
 * if id = null intent bundle can't pass it on
 */
@Parcelize
data class Category(
  @SerializedName("_id")
  val id: String = "",
  var items: List<Food>? = null,
  var name: InternationalizationContent? = null,
  val description: InternationalizationContent? = null,
  var type: String? = null,
  // the horizontal type item
  val rows: Int? = null,
  val groupId: String? = null,
  // group for header navigation
  val group: MenuBundle? = null,
  var expand: Int? = null,
  var limit: Int? = null,
  val maxItem: Int? = null,
  val image: ThemeImage? = null,
//    val categoryId: String? = null
  val promotion: Boolean? = null,
  val background: ThemeImage? = null,
  val balance: Int? = null
) : Parcelable

/**
 * ui used the menu category
 * show more item direct use Category
 */
data class MenuCategory(
  @SerializedName("_id")
  val id: String?,
  val name: InternationalizationContent? = null,
  val description: InternationalizationContent? = null,
  val type: String? = null,
  val groupId: String? = null,
  var expand: Int? = null,
  var limit: Int? = null,
  val maxItem: Int? = null,
  val image: ThemeImage? = null,
) {
  companion object {
    fun init(category: Category?): MenuCategory? {
      val category = category ?: return null
      return MenuCategory(
        category.id,
        category.name,
        category.description,
        category.type,
        category.groupId,
        category.expand,
        category.limit,
        category.maxItem,
        category.image,
      )
    }
  }
}

/**
 * menu v2 response bean
 */
data class MenuBean(
  val sections: List<Category?>? = null,
  val groups: List<MenuBundle>? = null,
  val bundles: List<Restaurant>? = null,
)

data class Menu(
  var food: List<Food>,
  var categories: List<Category>,
  var recommend: MenuRecommend,
  var reward: List<Food>?
)

@Parcelize
data class Food(
  @SerializedName("_id")
  val id: String = "",
  val analytics: FoodAnalytics? = null,
  var available: Boolean? = null,
  var category: FoodCategory? = null,
  val code: String? = null,
  val color: String? = null,
  val cost: Int = 0,
  val point: Int? = 0,
  val description: InternationalizationContent? = null,
  val featured: Boolean? = null,
  val hours: List<Hour>? = null,
  @Exclude
  var image: FoodImage? = null,
  var comboImage: ThemeImage? = null,
  var combo: List<Combo>? = null,
    /*
     * Whether to display pictures
     * when the current section is not a featured section
     * local variable default value need all param default value
     */
  var isHiddenImage: Boolean? = true,
//    val index: Int? = null,
  val label: InternationalizationContent? = null,
  val minimum: Int? = null,
  val name: InternationalizationContent = InternationalizationContent("", "", "", ""),
  @Exclude
  var options: MutableList<Option>? = null,
  val popularity: Int? = null,
  var price: Int = 0,
  var originalPrice: Int? = null,
  val regionZscore: Double? = null,
  val route: Route? = null,
//    @Exclude
//    var score: Score? = null,
  val skip: String? = null,
  val tag: List<String>? = null,
  val zscore: Double? = null,
  var timezone: String? = null,
  var recent: Boolean? = null,
  var count: Int? = null,
  var opt: MutableList<Item>? = null,
  // Save selected cart count for food
  var selectedCount: Int? = null,
  // Only for recommend food
  var recommend: InternationalizationContent? = null,
  // Food restaurant info
  var restaurant: RecommendationRestaurant? = null,
  // reward food
  var reward: Boolean? = null,
  // show image bool
  var showImage: Boolean? = null,
  // limit n order for each customer
  val limit: Int? = null,
  // use `DiscountCondition` for avoiding create a new one
  // only `DiscountCondition.minimum` has value now
  val condition: DiscountCondition? = null,
  // item background
  val background: ThemeImage? = null,
  @Exclude
  var isPlus: Boolean? = null,
  @Exclude
  var isMinus: Boolean? = null,
  val customerRating: CustomerRating? = null,
  val largeImage: LargeFoodImage? = null,
) : Parcelable {

  fun hasImage(): Boolean {
    return image?.url?.isNotEmpty() == true
  }

  fun humanizeRating() = customerRating?.score?.times(100)?.toInt()?.toString()?.let {
    " $it% " + if (customerRating.count != null) "(${customerRating.count})" else ""
  }

  fun hasRating() = customerRating?.score != null
}

@Parcelize
data class LargeFoodImage(val url: String? = null) : Parcelable

/**
 * Menu Recommend section
 */
data class MenuRecommend(
  var featured: List<Food>,
  var popular: List<Food>,
  var match: List<Food>? = null,
  var recent: List<Food>? = null
) {
  fun flatten(): List<Food> {
    return featured + (match ?: listOf()) + popular + (recent ?: listOf())
  }
}

/*
 * Food image struct
 * string is compatible with old image
 * object is new image struct
 */
@Parcelize
data class FoodImage(
  @SerializedName("_id")
  val id: String?,
  val url: String?,
  val noPlate: Boolean?,
  val createdAt: String?,
  val updatedAt: String?,
  // the market flag
  val placeholder: String?,
) : Parcelable {
  companion object {
    const val MARKET = "market"
    const val RESTAURANT = "restaurant"
  }
}

@Parcelize
data class Combo(
  @SerializedName("_id")
  val id: String?,
  val description: InternationalizationContent? = null,
  val name: InternationalizationContent = InternationalizationContent("", "", "", ""),
  val price: Int = 0,
  val originalPrice: Int? = null,
  val cost: Int = 0,
  val image: ComboFoodImage?,
) : Parcelable

@Parcelize
data class ComboFoodImage(
  @SerializedName("_id")
  val id: String?,
  val url: String?,
  val noPlate: Boolean?,
) : Parcelable

@Parcelize
data class Cart(
  @SerializedName("_id")
  val id: String = "",
  val price: Int = 0,
  val originalPrice: Int? = null,
  var point: Int? = null,
  val reward: Boolean? = null,
  val name: InternationalizationContent = InternationalizationContent("", "", "", ""),
  var opt: List<Item> = listOf(),
  // Opt of Cart
  var qty: Int? = null,
  var limit: Int? = null,
  val createdAt: Double = 0.0,
  var nickName: String? = null,
  // device UUID
  var ownerId: String? = null,
  val categoryMaxItem: Int? = null,
  val categoryId: String? = null,
  // id of bundle restaurant which this cartItem within
  val bundleRestId: String? = null,
  val bundleRestName: InternationalizationContent? = null,
  var bundleRestBalance: Int? = null,

  // This is used to store `condition.minimum`. No structure is introduced here to make it easier to initialize this value.
  val minimum: Int? = null,
  // show image in cart
  var foodImage: FoodImage? = null,

  @Exclude
  var tag: HamburgerButton.RotateType? = null
) : Parcelable {
  fun compareToOpt(cart: Cart): Boolean {
    if (this.opt.size != cart.opt.size)
      return false

    val pairList = this.opt.toMutableList().zip(cart.opt)

    return pairList.all { (elt1, elt2) ->
      elt1 == elt2
    }
  }
}

/*
 * cart and restaurant are bound
 */
data class RestaurantCart(
  var cartList: List<Cart>? = null,
  var restaurant: Restaurant? = null,
  var comments: String? = null,
)

@Parcelize
data class MenuHour(
  val end: Int,
  val dayOfWeek: Int,
  val start: Int
) : Parcelable

@Parcelize
data class Score(
  val region: Double?,
  val restaurant: Double?
) : Parcelable

@Parcelize
data class Route(
  @SerializedName("_id")
  val id: String
) : Parcelable

@Parcelize
data class Option(
  @SerializedName("_id")
  val id: String,
  var items: List<Item>? = null,
  var selected: MutableList<Item>? = null,
  val max: Int? = null,
  val min: Int? = null,
  val name: InternationalizationContent? = null
) : Parcelable

@Parcelize
data class Item(
  @SerializedName("_id")
  val id: String,
  val analytics: ItemAnalytics?,
  val available: Boolean?,
  val name: InternationalizationContent,
  val price: Int?,
  var count: Int? = null,
  val featured: Boolean? = null,
  @Exclude
  var tag: HamburgerButton.RotateType? = null,
  @Exclude
  var isPlus: Boolean? = null,
  @Exclude
  var isMinus: Boolean? = null,
) : Parcelable

@Parcelize
data class ItemAnalytics(val popularity: Int) : Parcelable

@Parcelize
data class FoodCategory(
  @SerializedName("_id")
  val id: String,

  // it's a temp cache for checkout to calculate total of a category
  // original value is from menu category maxItem
  var maxItem: Int? = null,
  var categoryType: String? = null,
  var categoryIndex: Int? = null,
  var restBalance: Int? = null,
  var foodIndex: Int? = null,

) : Parcelable

@Parcelize
data class FoodAnalytics(val history: List<Int>) : Parcelable

data class CategoryRestaurant(
  @SerializedName("_id")
  val id: String
)

data class OrderGroup(
  @SerializedName("_id")
  val id: String,
  val user: OrderGroupUser,
  // / Cart list grouped by user
  var list: List<CustomerCart>?,
  val restaurant: OrderGroupRest,
  // / Mark if this device is the owner
  val owner: Boolean,
  val groupId: String,
  // / Mark if group is closed
  val closed: Boolean,
  // / Save the order id if group place order success
  val orderId: String?
)

data class CustomerCart(
  val user: OrderGroupUser,
  var items: List<Cart>?
)

data class OrderGroupUser(
  val deviceId: String,
  val customerId: String?,
  val nickName: String
)

public data class UpdateOrderGroupReq(
  val items: List<Cart>,
  val deviceId: String,
  val groupId: String
)

public data class CreateOrderGroupReq(
  val customerId: String,
  val deviceId: String,
  val items: List<Cart>,
  val restaurant: OrderGroupRest,
  val owner: Boolean,
  val nickName: String,
  val groupId: String?
)

public data class OrderGroupRest(
  val id: String,
  val name: InternationalizationContent?
)

// / Food restaurant info
@Parcelize
data class RecommendationRestaurant(
  @SerializedName("_id")
  val id: String?,
  val name: InternationalizationContent?
) : Parcelable

@Parcelize
data class DiscountCondition(
  val minimum: Int?,
  val discount: Discount?,
  val name: InternationalizationContent? = null,
  val type: String? = null,
  val apply: Boolean? = null,
  val amount: Int? = null,
  val adjustments: Adjustments? = null
) : Parcelable {
  companion object {
    const val TYPE_MENU = "menu"
    const val TYPE_VIP = "vip"
    // the discount of ricepo by restaurant
    const val TYPE_CUSTOMER = "customer"
    const val TYPE_POOL = "pool"
    const val TYPE_UNIONPAY = "unionPay"
    const val TYPE_DELIVERY = "delivery"
  }
}
