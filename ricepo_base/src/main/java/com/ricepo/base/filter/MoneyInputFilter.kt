package com.ricepo.base.filter

import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.method.DigitsKeyListener

//
// Created by <PERSON><PERSON> on 3/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class MoneyInputFilter : DigitsKeyListener(false, true) {

  private var integerDigits = 2
  private var decimalDigits = 2

  fun setDigits(i: Int = 2, d: Int = 2): MoneyInputFilter {
    integerDigits = i
    decimalDigits = d
    return this
  }

  override fun filter(
    source: CharSequence,
    start: Int,
    end: Int,
    dest: Spanned,
    dstart: Int,
    dend: Int
  ): CharSequence {
    var source = source
    var start = start
    var end = end
    val out = super.filter(source, start, end, dest, dstart, dend)

    // if changed, replace the source
    if (out != null) {
      source = out
      start = 0
      end = out.length
    }

    val len = end - start

    // if deleting, source is empty
    // and deleting can't break anything
    if (len == 0) {
      return source
    }

    if (source.toString() == "." && dstart == 0) {
      return "0."
    }

    if (source.toString() != "." && dest.toString() == "0") {
      return ""
    }

    val dlen = dest.length

    // Find the position of the decimal .
    for (i in 0 until dstart) {
      if (dest[i] == '.') {
        // being here means, that a number has
        // been inserted after the dot
        // check if the amount of digits is right
        return if (dlen - (i + 1) + len > decimalDigits) "" else SpannableStringBuilder(source, start, end)
      } else if (source.toString() != "." && !dest.contains(".")) {
        // the length of number from LengthFilter
        var keep: Int = integerDigits - (dlen - (dend - dstart))
        return if (keep <= 0) {
          ""
        } else if (keep >= end - start) {
          SpannableStringBuilder(source, start, end) // keep original
        } else {
          keep += start
          if (Character.isHighSurrogate(source[keep - 1])) {
            --keep
            if (keep == start) {
              return ""
            }
          }
          source.subSequence(start, keep)
        }
      }
    }

    for (i in start until end) {
      if (source[i] == '.') {
        // being here means, dot has been inserted
        // check if the amount of digits is right
        // return new SpannableStringBuilder(source, start, end)
        return if (dlen - dend + (end - (i + 1)) > decimalDigits) "" else break
      }
    }

    // if the dot is after the inserted part,
    // nothing can break
    return SpannableStringBuilder(source, start, end)
  }
}
