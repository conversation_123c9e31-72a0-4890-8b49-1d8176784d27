package com.ricepo.base.model.parser

import com.google.gson.JsonElement
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.OrderDelivery
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 30/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OrderDeliverySerializer : JsonSerializer<OrderDelivery> {

  override fun serialize(src: OrderDelivery?, typeOfSrc: Type?, context: JsonSerializationContext?): JsonElement {

    val jsonElement = ParserFacade.gson.toJsonTree(src)

    val address = src?.address
    if (address is AddressObj) {
      jsonElement.asJsonObject.add("address", ParserFacade.gson.toJsonTree(address))
    } else {
      jsonElement.asJsonObject.add("address", context?.serialize(address))
    }

    return jsonElement
  }
}
