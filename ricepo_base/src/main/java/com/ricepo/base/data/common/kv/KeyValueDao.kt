package com.ricepo.base.data.common.kv

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.ricepo.base.parser.ParserFacade
import java.util.Date

//
// Created by <PERSON><PERSON> on 20/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Dao
abstract class KeyValueDao {

  @Query("select f_value from t_value where f_key in (:keys) order by updated_at desc")
  abstract fun getValue(keys: List<String>): String

  @Query("select * from t_value where f_key in (:keys) order by updated_at desc")
  abstract fun getValueEntity(keys: List<String>): KeyValueEntity?

  @Query("select f_value from t_value where f_key in (:keys) order by updated_at desc")
  abstract suspend fun getValueSuspend(keys: List<String>): String

  @Query("delete from t_value where f_key=:key")
  abstract suspend fun deleteValue(key: String): Int

  @Query("update t_value set f_value=:value where f_key=:key")
  abstract suspend fun updateValue(key: String, value: String): Int

  // with entity primary common
  @Update
  abstract suspend fun updateValue(entity: KeyValueEntity): Int

  @Insert
  abstract suspend fun insertValue(entity: KeyValueEntity)

  /**
   * save value by key
   * isUpdateAt is update the updateAt time
   */
  @Transaction
  open suspend fun saveValue(key: String, data: Any, isUpdateAt: Boolean = true) {
    if (key.isNullOrEmpty()) return
    // direct to save string and save object to json
    val gson = ParserFacade.buildGson()
    val value = if (data is String) data else gson.toJson(data)
    val kv = KeyValueEntity(key, value)
    try {
      val original = getValueEntity(listOf(key))
      if (!original?.value.isNullOrEmpty()) {
        if (isUpdateAt) {
          kv.setUpdatedAt(Date())
        } else {
          kv.setUpdatedAt(original?.getUpdatedAt())
        }
        updateValue(kv)
      } else {
        kv.setUpdatedAt(Date())
        insertValue(kv)
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }
}
