package com.ricepo.base.model.parser

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.ricepo.base.model.Food
import com.ricepo.base.model.FoodImage
import com.ricepo.base.model.Option
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 9/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class FoodDeserializer : JsonDeserializer<Food> {
  override fun deserialize(
    json: JsonElement?,
    typeOfT: Type?,
    context: JsonDeserializationContext?
  ): Food {

    json as JsonObject

    val gson = ParserFacade.gson

    val food = gson.fromJson(json, Food::class.java)

    // options
    var optionsElement = json.get("options")
    if (optionsElement != null) {
      if (optionsElement.isJsonObject) {
        val options = gson.fromJson<MutableList<Option>>(optionsElement, object : TypeToken<List<Option>>() {}.type)
        food.options = options
      } else {
        try {
          food.options = gson.fromJson(optionsElement.toString(), object : TypeToken<List<Option>>() {}.type)
        } catch (e: Exception) {
          e.printStackTrace()
          food.options = null
        }
      }
    }

    // score
//        var scoreElement = json.get("score")
//        if (scoreElement != null) {
//            try {
//                if (scoreElement.isJsonObject) {
//                    val score = gson.fromJson<Score>(scoreElement, object: TypeToken<Score>(){}.type)
//                    food.score = score
//                } else if (scoreElement?.asNumber != null) {
//                    food.score = null
//                }
//            } catch (e: Exception) {
//                e.printStackTrace()
//                food.score = null
//            }
//        }

    // image
    var imageElement = json.get("image")
    if (imageElement != null) {
      try {
        if (imageElement.isJsonObject) {
          val image = gson.fromJson<FoodImage>(imageElement, object : TypeToken<FoodImage>() {}.type)
          food.image = image
        }
      } catch (e: Exception) {
        e.printStackTrace()
        food.image = null
      }
    }

    return food
  }
}
