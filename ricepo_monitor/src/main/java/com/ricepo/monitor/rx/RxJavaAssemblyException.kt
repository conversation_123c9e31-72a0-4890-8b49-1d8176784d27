/*
 * Copyright 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ricepo.monitor.rx

/**
 * Holds onto the assembly stacktrace.
 */
class RxJavaAssemblyException : RuntimeException() {
  val stacktrace: String

  /**
   * Returns the captured and filtered stacktrace.
   * @return the captured and filtered stacktrace
   */
  fun stacktrace(): String {
    return stacktrace
  }

  @Synchronized
  override fun fillInStackTrace(): Throwable {
    // don't store own stacktrace
    // empty stacktrace prevents crashes on some JVMs when `getStackTrace()` is invoked
    stackTrace = arrayOfNulls(0)
    return this
  }

  /**
   * Try appending this RxJavaAssemblyException as the very last cause of
   * the given throwable.
   * @param ex the Throwable to append to
   * @return ex
   */
  fun appendLast(ex: Throwable?): Throwable? {
    var ex = ex
    val r = ex
    val memory: MutableSet<Throwable?> = HashSet()
    while (ex!!.cause != null) {
      ex = if (memory.add(ex)) {
        ex.cause
      } else {
        // didn't work
        return r
      }
    }
    try {
      ex.initCause(this)
    } catch (exc: Throwable) {
      // didn't work, oh well
    }
    return r
  }

  companion object {
    private const val serialVersionUID = -6757520270386306081L
    fun buildStackTrace(): String {
      val b = StringBuilder()
      val es = Thread.currentThread().stackTrace
      b.append("RxJavaAssemblyException: assembled\r\n")
      for (e in es) {
        if (filter(e)) {
          b.append("at ").append(e).append("\r\n")
        }
      }
      return b.toString()
    }

    /**
     * Filters out irrelevant stacktrace entries.
     * @param e the stacktrace element
     * @return true if the element may pass
     */
    private fun filter(e: StackTraceElement): Boolean {
      // ignore bridge methods
      if (e.lineNumber == 1) {
        return false
      }
      val cn = e.className
      if (cn.contains("java.lang.Thread")) {
        return false
      }

      // ignore JUnit elements
      if (cn.contains("junit.runner") ||
        cn.contains("org.junit.internal") ||
        cn.contains("junit4.runner")
      ) {
        return false
      }

      // ignore reflective accessors
      if (cn.contains("java.lang.reflect") ||
        cn.contains("sun.reflect")
      ) {
        return false
      }

      // ignore RxJavaAssemblyException itself
      if (cn.contains(".RxJavaAssemblyException")) {
        return false
      }

      // the shims injecting the error
      return if (cn.contains("OnAssembly") ||
        cn.contains("RxJavaAssemblyTracking") ||
        cn.contains("RxJavaPlugins")
      ) {
        false
      } else true
    }

    /**
     * Tries to locate the RxJavaAssemblyException in the chain of causes of the
     * given Throwable.
     * @param ex the Throwable to start scanning
     * @return the RxJavaAssemblyException found or null
     */
    fun find(ex: Throwable?): RxJavaAssemblyException? {
      var ex = ex
      val memory: MutableSet<Throwable> = HashSet()
      while (ex != null) {
        if (ex is RxJavaAssemblyException) {
          return ex
        }
        ex = if (memory.add(ex)) {
          ex.cause
        } else {
          return null
        }
      }
      return null
    }
  }

  init {
    stacktrace = buildStackTrace()
  }
}
