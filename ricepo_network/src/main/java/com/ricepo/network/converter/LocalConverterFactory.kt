package com.ricepo.network.converter

import com.google.gson.Gson
import com.google.gson.TypeAdapter
import com.google.gson.reflect.TypeToken
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 25/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * A [converter][Converter.Factory] which uses Gson for JSON.
 *
 *
 * Because Gson is so flexible in the types it supports, this converter assumes that it can handle
 * all types. If you are mixing JSON serialization with something else (such as protocol buffers),
 * you must [add this instance][Retrofit.Builder.addConverterFactory]
 * last to allow the other converters a chance to see their types.
 */
class LocalConverterFactory private constructor(private val gson: Gson) : Converter.Factory() {

  override fun responseBodyConverter(
    type: Type,
    annotations: Array<Annotation>,
    retrofit: Retrofit
  ): Converter<ResponseBody, *> {

    val adapter = gson.getAdapter(TypeToken.get(type))

    val rawType = getRawType(type)
    if (rawType == Object::class.java || rawType == String::class.java) {
      return Converter<ResponseBody, Any> { value ->
        if (value.contentType() == "text/html; charset=utf-8".toMediaType()) {
          // the response is string to converter
          value.string()
        } else {
          GsonResponseBodyConverter<Any>(gson, adapter)
        }
      }
    }

    return GsonResponseBodyConverter<Any>(gson, adapter)
  }

  override fun requestBodyConverter(
    type: Type,
    parameterAnnotations: Array<Annotation>,
    methodAnnotations: Array<Annotation>,
    retrofit: Retrofit
  ): Converter<*, RequestBody> {
    val adapter: TypeAdapter<in Any> = gson.getAdapter(TypeToken.get(type)) as TypeAdapter<in Any>
    return GsonRequestBodyConverter(gson, adapter)
  }

  companion object {

    /**
     * Create an instance using a default [Gson] instance for conversion. Encoding to JSON and
     * decoding from JSON (when no charset is specified by a header) will use UTF-8.
     */
    @JvmOverloads // Guarding public API nullability.
    fun create(gson: Gson? = Gson()): LocalConverterFactory {
      if (gson == null) throw NullPointerException("gson == null")
      return LocalConverterFactory(gson)
    }
  }
}
