package com.ricepo.base.lifecycle

import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.disposables.Disposable

//
// Created by <PERSON><PERSON> on 20/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class LifecycleDisposables {

  private val disposeOnPause = CompositeDisposable()
  private val disposeOnStop = CompositeDisposable()
  private val disposeOnDestroyView = CompositeDisposable()
  private val disposeOnDestroy = CompositeDisposable()

  internal fun add(state: State, disposable: Disposable) {
    when (state) {
      State.Pause -> disposeOnPause.add(disposable)
      State.Stop -> disposeOnStop.add(disposable)
      State.DestroyView -> disposeOnDestroyView.add(disposable)
      State.Destroy -> disposeOnDestroy.add(disposable)
    }
  }

  internal fun dispose(state: State) {
    when (state) {
      State.Destroy -> {
        disposeOnPause.clear()
        disposeOnStop.clear()
        disposeOnDestroyView.clear()
        disposeOnDestroy.clear()
      }
      State.DestroyView -> {
        disposeOnPause.clear()
        disposeOnStop.clear()
        disposeOnDestroyView.clear()
      }
      State.Stop -> {
        disposeOnPause.clear()
        disposeOnStop.clear()
      }
      State.Pause -> {
        disposeOnPause.clear()
      }
    }
  }

  internal sealed class State {
    object Pause : State()
    object Stop : State()
    object DestroyView : State()
    object Destroy : State()
  }
}
