package com.ricepo.base.extension

import android.app.Activity
import com.ricepo.base.animation.Loading
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.Schedulers

//
// Created by <PERSON><PERSON> on 27/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

fun <T : Any> Single<T>.bindLoading(context: Activity?): Single<T> {
  return this
    .subscribeOn(Schedulers.io())
    .observeOn(AndroidSchedulers.mainThread())
    .doOnSubscribe {
      Loading.showLoading((context))
    }
    .doAfterSuccess {
      Loading.hideLoading()
    }
    .doOnError {
      Loading.hideLoading()
    }
}

fun <T : Any> Observable<T>.bindLoading(context: Activity?): Observable<T> {
  return this
    .subscribeOn(Schedulers.io())
    .observeOn(AndroidSchedulers.mainThread())
    .doOnSubscribe {
      Loading.showLoading((context))
    }
    .doAfterNext {
      Loading.hideLoading()
    }
    .doOnError {
      Loading.hideLoading()
    }
}

fun <T : Any> Observable<T>.bindLoadingWithoutNext(context: Activity?): Observable<T> {
  return this
    .subscribeOn(Schedulers.io())
    .observeOn(AndroidSchedulers.mainThread())
    .doOnSubscribe {
      Loading.showLoading((context))
    }
    .doOnError {
      Loading.hideLoading()
    }
}

// fun <T> Observable<T>.uiSubscribe(schedulers: PostExecutorThread, subscriber: Subscriber<in T>): Subscription {
//    return subscribeOn(schedulers.io)
//        .observeOn(schedulers.mainThread)
//        .subscribe(subscriber)
// }

fun <T : Any> Single<T>.uiSubscribe(): Single<T> {
  return subscribeOn(Schedulers.io())
    .observeOn(AndroidSchedulers.mainThread())
}

fun <T : Any> Observable<T>.uiSubscribe(): Observable<T> {
  return subscribeOn(Schedulers.io())
    .observeOn(AndroidSchedulers.mainThread())
}
