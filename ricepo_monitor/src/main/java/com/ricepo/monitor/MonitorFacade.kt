package com.ricepo.monitor

import android.content.Context
//import io.sentry.Sentry
//import io.sentry.SentryEvent
//import io.sentry.SentryLevel
//import io.sentry.SentryOptions
//import io.sentry.android.core.SentryAndroid
//import io.sentry.android.core.SentryAndroidOptions
//import io.sentry.protocol.Message

//
// Created by <PERSON><PERSON> on 14/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
object MonitorFacade {

  /**
   * manual initialization need android manifest default false
   */
//  fun initSentry(context: Context, isDebug: Boolean) {
//    if (isDebug) return
//    SentryAndroid.init(
//      context,
//      Sentry.OptionsConfiguration { options: SentryAndroidOptions ->
//        // Add a callback that will be used before the event is sent to Sentry.
//        // With this callback, you can modify the event or, when returning null, also discard the event.
//        options.beforeSend =
//          SentryOptions.BeforeSendCallback { event: SentryEvent, hint: Any? ->
//            if (SentryLevel.DEBUG == event.level ||
//              "debug" == event.environment
//            ) null else event
//          }
//      }
//    )
//  }

//  /**
//   * test sentry
//   */
//  fun testSentry() {
//    try {
//      throw Exception("This is a test.")
//    } catch (e: Exception) {
//      Sentry.captureException(e)
//    }
//  }
//
//  /**
//   * monitor capture message
//   */
//  fun captureMessage(msg: String) {
//    Sentry.captureMessage(msg)
//  }
//
//  /**
//   * monitor capture event
//   */
//  fun captureException(e: Throwable?, extras: Map<String, Any>? = null) {
//    val message = e?.message ?: return
//    val inExtras = mutableMapOf<String, Any>(EventLabel.CAUSE to (e?.stackTraceToString() ?: ""))
//    if (extras != null) {
//      inExtras.putAll(extras)
//    }
//    val event = MonitorEvent(
//      message = message,
//      environment = BuildConfig.BUILD_TYPE,
//      level = EventLevel.WARNING,
//      extras = inExtras
//    )
//    captureEvent(event)
//  }
//
//  /**
//   * monitor capture event
//   */
//  fun captureEvent(event: MonitorEvent) {
//    val message = Message()
//    message.message = event.message
//    val sentryEvent = SentryEvent()
//    sentryEvent.message = message
//    sentryEvent.setExtras(event.extras)
//    sentryEvent.environment = event.environment
//    sentryEvent.level = level(event.level)
//
//    actualSentryEvent(sentryEvent)
//  }
//
//  // convert level to SentryLevel
//  private fun level(level: EventLevel?): SentryLevel? {
//    return when (level) {
//      EventLevel.LOG -> SentryLevel.INFO
//      EventLevel.DEBUG -> SentryLevel.DEBUG
//      EventLevel.ERROR -> SentryLevel.ERROR
//      EventLevel.FATAL -> SentryLevel.FATAL
//      EventLevel.INFO -> SentryLevel.INFO
//      EventLevel.WARNING -> SentryLevel.WARNING
//      else -> SentryLevel.DEBUG
//    }
//  }
//
//  /**
//   * sentry send event
//   */
//  private fun actualSentryEvent(sentryEvent: SentryEvent) {
//    Sentry.captureEvent(sentryEvent)
//  }
}
