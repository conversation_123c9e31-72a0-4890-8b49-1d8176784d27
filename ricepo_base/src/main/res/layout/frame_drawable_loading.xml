<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/colorPrimary"
    android:focusableInTouchMode="true"
    android:clickable="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ricepo.style.view.DrawableAnimationView
        android:id="@+id/v_drawable_loading"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_loading"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_width="260dp"
        android:layout_height="180dp"
        android:layout_gravity="center" />

    <TextView
        android:id="@+id/tv_loading"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v_drawable_loading"
        style="@style/SubTitleStyle"
        android:layout_marginTop="-10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>