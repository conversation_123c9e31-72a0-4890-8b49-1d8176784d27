package com.ricepo.monitor.aspect

import android.view.View

//
// Created by <PERSON><PERSON> on 18/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object SingleClickHandle {

  var mLastClickTime: Long = 0

  var mLastClickViewId: Int? = null

  var mLastRouteName: String? = null

  fun isFastDoubleClick(v: View, intervalMillis: Long): Boolean {
    val viewId: Int = v.getId()
    val time = System.currentTimeMillis()
    val timeInterval: Long = Math.abs(time - mLastClickTime)
    return if (timeInterval < intervalMillis && viewId == mLastClickViewId) {
      true
    } else {
      mLastClickTime = time
      mLastClickViewId = viewId
      false
    }
  }

  fun isFastDoubleClick(routerName: String, intervalMillis: Long): Boolean {
    val time = System.currentTimeMillis()
    val timeInterval: Long = Math.abs(time - mLastClickTime)
    return if (timeInterval < intervalMillis && routerName == mLastRouteName) {
      true
    } else {
      mLastClickTime = time
      mLastRouteName = routerName
      false
    }
  }
}
