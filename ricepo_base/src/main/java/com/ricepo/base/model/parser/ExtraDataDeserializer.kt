package com.ricepo.base.model.parser

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.ricepo.base.model.ExtraData
import com.ricepo.base.model.ExtraDataValue
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 9/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ExtraDataDeserializer : JsonDeserializer<ExtraData> {
  override fun deserialize(
    json: JsonElement?,
    typeOfT: Type?,
    context: JsonDeserializationContext?
  ): ExtraData {

    json as JsonObject

    val gson = ParserFacade.gson
    val data = gson.fromJson(json, ExtraData::class.java)

    // value
    var valueElement = json.get("value")
    if (valueElement != null) {
      if (valueElement.isJsonObject) {
        try {
          val options = gson.fromJson<HashMap<String, Double>>(
            valueElement,
            object : TypeToken<HashMap<String, Double>>() {}.type
          )
          data.value = ExtraDataValue.map(options)
        } catch (e: Exception) {
          e.printStackTrace()
          try {
            data.value = ExtraDataValue.number(valueElement.asNumber)
          } catch (e: java.lang.Exception) {
            e.printStackTrace()
            try {
              data.value = ExtraDataValue.string(valueElement.asString)
            } catch (e: java.lang.Exception) {
              e.printStackTrace()
            }
          }
        }
      } else if (!valueElement.isJsonNull) {
        try {
          data.value = ExtraDataValue.number(valueElement.asNumber)
        } catch (e: java.lang.Exception) {
          e.printStackTrace()
          try {
            data.value = ExtraDataValue.string(valueElement.asString)
          } catch (e: java.lang.Exception) {
            e.printStackTrace()
          }
        }
      }
    }

    return data
  }
}
