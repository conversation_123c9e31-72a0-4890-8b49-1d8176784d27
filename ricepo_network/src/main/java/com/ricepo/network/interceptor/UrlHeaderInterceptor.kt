package com.ricepo.network.interceptor

import com.ricepo.base.tools.SystemUtils
import com.ricepo.network.NetworkConfiguration
import okhttp3.Interceptor
import okhttp3.Response

//
// Created by <PERSON><PERSON> on 27/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class UrlHeaderInterceptor(private val configuration: NetworkConfiguration) : Interceptor {

  override fun intercept(chain: Interceptor.Chain): Response {

    val original = chain.request()
    val requestBuilder = original.newBuilder()

    val token = configuration.getToken()
    requestBuilder.addHeader("Authorization", "JWT $token")

    // todo replace these header with rice later
    requestBuilder.addHeader("Content-Type", "application/json")
//        requestBuilder.addHeader("X-Ricepo-Client", "Ricepo/${"6.5.8"}")
    requestBuilder.addHeader("X-Ricepo-Client", "Ricepo/${SystemUtils.versionName()}")
    requestBuilder.addHeader("X-Ricepo-Device", configuration.uuid())
    requestBuilder.addHeader("X-Ricepo-Lang", configuration.lang())

    val request = requestBuilder.url(original.url.newBuilder().build()).build()

    return chain.proceed(request)
  }
}
