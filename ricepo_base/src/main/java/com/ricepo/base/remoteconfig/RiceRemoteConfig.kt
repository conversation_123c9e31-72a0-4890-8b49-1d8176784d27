package com.ricepo.base.remoteconfig

import com.google.gson.Gson
import com.ricepo.base.remoteconfig.base.KonfigModel
import com.ricepo.base.remoteconfig.base.konfig

interface IRiceRemoteConfig {
  fun shouldShowRedeem(country: String?): <PERSON><PERSON><PERSON>
}


object RiceRemoteConfig : KonfigModel, IRiceRemoteConfig {

  private const val SEP = ","
  private const val SHOW_REDEEM_AREA = "area_showing_redemption"
  private const val COMPLETE_PROFILE_COUPON_AMOUNT = "coupon_amount_of_profile_completion"
  private const val REFERRAL_CONFIG = "referral_config"

  private val showRedeemAreas by konfig(SHOW_REDEEM_AREA, "")

  val completeProfileCouponAmount by konfig(COMPLETE_PROFILE_COUPON_AMOUNT, 300)

  private val referralConfig by konfig(REFERRAL_CONFIG, "")

  override fun shouldShowRedeem(country: String?) = showRedeemAreas.split(SEP).any {
    it.equals(country, ignoreCase = true)
  }

  private val gson: Gson by lazy {
    Gson()
  }

}
