package com.ricepo.base.view

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager

//
// Created by <PERSON><PERSON> on 23/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class NoScrollLinearLayoutManager(context: Context?) : LinearLayoutManager(context) {
  private var scrollable = true

  fun enableScrolling() {
    scrollable = true
  }

  fun disableScrolling() {
    scrollable = false
  }

  override fun canScrollVertically() =
    super.canScrollVertically() && scrollable

  override fun canScrollHorizontally() =
    super.canScrollVertically() && scrollable
}
