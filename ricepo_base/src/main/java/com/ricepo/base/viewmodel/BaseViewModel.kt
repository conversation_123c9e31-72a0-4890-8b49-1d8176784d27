package com.ricepo.base.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ricepo.base.BaseApplication
import com.ricepo.base.data.common.CommonDatabase
import com.ricepo.base.data.common.kv.KEY_AUTH_TOKEN
import com.ricepo.base.data.common.kv.KEY_CUSTOMER
import com.ricepo.base.model.Customer
import com.ricepo.base.parser.ParserFacade
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

//
// Created by <PERSON><PERSON> on 21/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class BaseViewModel : ViewModel() {

  /**
   * manager view model subscribe dispose
   */

  fun checkLogin(): MutableLiveData<String> {
    val liveData = MutableLiveData<String>()
    viewModelScope.launch(Dispatchers.IO) {
      val token = CommonDatabase.getInstance(BaseApplication.context)
        .keyValueDao().getValue(listOf(KEY_AUTH_TOKEN))
      liveData.postValue(token)
    }
    return liveData
  }

  private var isFirstInit = true
  private var mCustomer: Customer? = null

  /**
   * pair first is login changed
   * pair second is first loaded
   */
  private val _loginState = MutableLiveData<LoginBread>()
  val loginState: LiveData<LoginBread> = _loginState
  private var job: Job? = null
  fun checkLoginChange() {
    job?.cancel()
    job = viewModelScope.launch(Dispatchers.IO) {
      val customerStr = CommonDatabase.getInstance(BaseApplication.context)
        .keyValueDao().getValue(listOf(KEY_CUSTOMER))
      val customer = try {
        ParserFacade.gson.fromJson(
          customerStr, Customer::class.java
        )
      } catch (e: Exception) {
        e.printStackTrace()
        null
      }
      val isLoginAndVipChanged = (customer != mCustomer) ||
        (
          customer?.subscription?.currentPeriodEndAt
            != mCustomer?.subscription?.currentPeriodEndAt
          )
      _loginState.postValue(LoginBread(isFirstInit, isLoginAndVipChanged, customer))
      mCustomer = customer
      isFirstInit = false
    }
  }

  data class LoginBread(
    val isFirstCreate: Boolean,
    val isLoginAndVipChange: Boolean,
    val savedCustomer: Customer?
  )
}

/**
 * used of observable combine latest and with latest form
 */
typealias RxBiFunction<T1, T2, R> = io.reactivex.rxjava3.functions.BiFunction<T1, T2, R>

typealias RxFunction3<T1, T2, T3, R> = io.reactivex.rxjava3.functions.Function3<T1, T2, T3, R>
