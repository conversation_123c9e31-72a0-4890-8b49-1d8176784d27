<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@id/lay_title"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_title_icon"
        android:src="@drawable/ic_close"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_title_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="iv_close"
        app:barrierDirection="end"
        tools:ignore="UnknownId" />

    <TextView
        android:id="@id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:includeFontPadding="false"
        android:gravity="center"
        android:singleLine="true"
        android:ellipsize="end"
        android:paddingRight="52dp"
        style="@style/TitleStyle"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toRightOf="@+id/barrier_title_left"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>