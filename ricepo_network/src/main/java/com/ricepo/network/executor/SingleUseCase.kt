package com.ricepo.network.executor

import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import io.reactivex.rxjava3.schedulers.Schedulers

abstract class SingleUseCase<T : Any, in Params> constructor(
  private val postExecutionThread: PostExecutionThread
) {

  private val disposables = CompositeDisposable()

  protected abstract fun buildUseCaseObservable(params: Params? = null): Single<T>

  /**
   * execute rx observable
   */
  open fun execute(singleObserver: DisposableSingleObserver<T>, params: Params? = null) {
    val single = this.buildUseCaseObservable(params)
      .subscribeOn(Schedulers.io())
      .observeOn(postExecutionThread.mainScheduler) as Single<T>
    addDisposable(single.subscribeWith(singleObserver))
  }

  private fun addDisposable(disposable: Disposable) {
    if (!disposable.isDisposed) {
      disposables.add(disposable)
    }
  }

  open fun dispose() {
    disposables.dispose()
  }
}
