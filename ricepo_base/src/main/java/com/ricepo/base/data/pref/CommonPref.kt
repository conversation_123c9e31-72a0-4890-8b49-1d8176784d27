package com.ricepo.base.data.pref

import android.content.Context
import com.google.gson.Gson
import com.ricepo.base.BaseApplication
import com.ricepo.base.tools.SystemUtils

//
// Created by <PERSON><PERSON> on 21/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

interface PrefDataSource {
  fun cachePopups(ids: Set<String>)
  fun getPopups(): Set<String>
}

object CommonPref : PrefDataSource {

  private const val PREF_CACHE = "pref_cache"

  /**
   * the refresh point of restaurant page
   */
  private const val KEY_RESTAURANT_POINT = "restaurant_point"

  /**
   * the flag of check update
   */
  private const val KEY_CHECK_UPDATE = "check_update"

  /**
   * the flag of driver rating on restaurant list page
   */
  private const val KEY_DRIVER_RATING = "driver_rating"

  /**
   * the flag of check promo info
   */
  private const val KEY_CHECK_PROMO = "check_promo_info"

  /**
   * the flag of app rating
   */
  private const val KEY_APP_RATING = "app_rating"

  /**
   * the flag of update address for order alert
   */
  private const val KEY_ORDER_UPDATE_ADDRESS = "order_update_address"

  /**
   * the country code of address
   */
  private const val KEY_ADDRESS_COUNTRY_CODE = "address_country_code"

  private const val KEY_POP_UP_IDS = "key_pop_up_ids"

  /**
   * branch refer code
   */
  private const val KEY_BRANCH_REFER_CODE = "branch_refer_code"

  fun saveRestaurantPoint(context: Context, value: String) {
    savePref(context, KEY_RESTAURANT_POINT, value)
  }

  fun getRestaurantPoint(context: Context): String? {
    return getPref(context, KEY_RESTAURANT_POINT)
  }

  fun saveCheckUpdate(context: Context, update: String) {
    savePref(context, KEY_CHECK_UPDATE, update)
  }

  fun getCheckUpdate(context: Context): String? {
    return getPref(context, KEY_CHECK_UPDATE)
  }

  fun removeCheckUpdate(context: Context) {
    removePref(context, KEY_CHECK_UPDATE)
  }

  fun saveDriverRating(context: Context, update: String) {
    savePref(context, KEY_DRIVER_RATING, update)
  }

  fun gettDriverRating(context: Context): String? {
    return getPref(context, KEY_DRIVER_RATING)
  }

  fun removeDriverRating(context: Context) {
    removePref(context, KEY_DRIVER_RATING)
  }

  fun saveCheckPromo(context: Context, update: String) {
    savePref(context, KEY_CHECK_PROMO, update)
  }

  fun getCheckPromo(context: Context): String? {
    return getPref(context, KEY_CHECK_PROMO)
  }

  fun removeCheckPromo(context: Context) {
    removePref(context, KEY_CHECK_PROMO)
  }

  /**
   */
  fun saveCheckPromoForRegion(context: Context, regionId: String, update: String) {
    val key = "${KEY_CHECK_PROMO}_$regionId"
    savePref(context, key, update)
  }

  fun getCheckPromoForRegion(context: Context, regionId: String): String? {
    val key = "${KEY_CHECK_PROMO}_$regionId"
    return getPref(context, key)
  }

  fun removeCheckPromoForRegion(context: Context, regionId: String) {
    val key = "${KEY_CHECK_PROMO}_$regionId"
    removePref(context, key)
  }

  fun clearAllRegionPromoTimes(context: Context) {
    val sharePref = context.getSharedPreferences(PREF_CACHE, Context.MODE_PRIVATE)
    val editor = sharePref.edit()

    // 获取所有以KEY_CHECK_PROMO_开头的key
    val allKeys = sharePref.all.keys
    allKeys.filter { it.startsWith("${KEY_CHECK_PROMO}_") }.forEach { key ->
      editor.remove(key)
    }

    editor.apply()
  }

  fun saveAppRating(context: Context) {
    savePref(context, KEY_APP_RATING, SystemUtils.versionCode().toString())
  }

  fun getAppRating(context: Context): String? {
    return getPref(context, KEY_APP_RATING)
  }

  fun saveOrderUpdateAddress(context: Context, orderId: String) {
    val json = getPref(context, KEY_ORDER_UPDATE_ADDRESS)
    try {
      val gson = Gson()
      val orders = if (json != null) {
        gson.fromJson<List<String>>(json, List::class.java).toMutableList()
      } else {
        mutableListOf()
      }
      if (!orders.contains(orderId)) {
        orders.add(orderId)
      }
      if (orders.size > 10) {
        orders.removeAt(0)
      }
      savePref(context, KEY_ORDER_UPDATE_ADDRESS, gson.toJson(orders))
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  fun getOrderUpdateAddress(context: Context, orderId: String): String? {
    val json = getPref(context, KEY_ORDER_UPDATE_ADDRESS) ?: return null
    var result: String? = null
    try {
      val orders = Gson().fromJson<List<String>>(json, List::class.java)
      result = if (orders.contains(orderId)) {
        orderId
      } else {
        null
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return result
  }

  fun saveCountryCode(countryCode: String?) {
    savePref(BaseApplication.context, KEY_ADDRESS_COUNTRY_CODE, countryCode ?: "")
  }

  override fun cachePopups(ids: Set<String>) {
    val popups = getPopups()
    popups.toMutableSet().apply {
      addAll(ids)
    }.joinToString(separator = ",").let {
      savePref(BaseApplication.context, KEY_POP_UP_IDS, it)
    }
  }

  override fun getPopups(): Set<String> {
    return getPref(
      BaseApplication.context, KEY_POP_UP_IDS
    )?.split(",")?.toSet() ?: setOf()
  }

  /**
   * get the address country code
   */
  fun getCountryCode(): String? {
    return getPref(BaseApplication.context, KEY_ADDRESS_COUNTRY_CODE)
  }

  /**
   * save the key -> value to perf
   */
  fun savePref(context: Context, key: String, value: String) {
    val sharePref = context.getSharedPreferences(PREF_CACHE, Context.MODE_PRIVATE)
    with(sharePref.edit()) {
      putString(key, value)
      commit()
    }
  }

  /**
   * get the value by [key] from perf
   */
  fun getPref(context: Context, key: String): String? {
    val sharePref = context.getSharedPreferences(PREF_CACHE, Context.MODE_PRIVATE)
    return sharePref.getString(key, null)
  }

  private fun removePref(context: Context, key: String) {
    val sharePref = context.getSharedPreferences(PREF_CACHE, Context.MODE_PRIVATE)
    with(sharePref.edit()) {
      remove(key)
      commit()
    }
  }
}
