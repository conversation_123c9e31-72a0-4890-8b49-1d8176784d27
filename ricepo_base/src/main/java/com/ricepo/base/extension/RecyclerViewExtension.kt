package com.ricepo.base.extension

import android.annotation.SuppressLint
import android.view.MotionEvent
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import java.lang.Math.abs

//
// Created by <PERSON><PERSON> on 24/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * nested scrolling int opposite direction
 */
fun RecyclerView.enforceSingleScrollDirection() {
  val enforcer = SingleScrollDirectionEnforcer()
  addOnItemTouchListener(enforcer)
  addOnScrollListener(enforcer)
}

private class SingleScrollDirectionEnforcer :
  RecyclerView.OnScrollListener(),
  RecyclerView.OnItemTouchListener {

  private var scrollState = RecyclerView.SCROLL_STATE_IDLE
  private var scrollPointerId = -1
  private var initialTouchX = 0
  private var initialTouchY = 0
  private var dx = 0
  private var dy = 0

  override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
    when (e.actionMasked) {
      MotionEvent.ACTION_DOWN -> {
        scrollPointerId = e.getPointerId(0)
        initialTouchX = (e.x + 0.5f).toInt()
        initialTouchY = (e.y + 0.5f).toInt()
      }
      MotionEvent.ACTION_POINTER_DOWN -> {
        val actionIndex = e.actionIndex
        scrollPointerId = e.getPointerId(actionIndex)
        initialTouchX = (e.getX(actionIndex) + 0.5f).toInt()
        initialTouchY = (e.getY(actionIndex) + 0.5f).toInt()
      }
      MotionEvent.ACTION_MOVE -> {
        val index = e.findPointerIndex(scrollPointerId)
        if (index >= 0 && scrollState != RecyclerView.SCROLL_STATE_DRAGGING) {
          val x = (e.getX(index) + 0.5f).toInt()
          val y = (e.getY(index) + 0.5f).toInt()
          dx = x - initialTouchX
          dy = y - initialTouchY
        }
      }
    }
    return false
  }

  override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {}

  override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {}

  override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
    val oldState = scrollState
    scrollState = newState
    if (oldState == RecyclerView.SCROLL_STATE_IDLE && newState == RecyclerView.SCROLL_STATE_DRAGGING) {
      val layoutManager = recyclerView.layoutManager
      if (layoutManager != null) {
        val canScrollHorizontally = layoutManager.canScrollHorizontally()
        val canScrollVertically = layoutManager.canScrollVertically()
        if (canScrollHorizontally != canScrollVertically) {
          if (canScrollHorizontally && abs(dy) > abs(dx)) {
            recyclerView.stopScroll()
          }
          if (canScrollVertically && abs(dx) > abs(dy)) {
            recyclerView.stopScroll()
          }
        }
      }
    }
  }
}

/**
 * recycler is scroll bottom
 */
fun RecyclerView.isVisibleBottom(): Boolean {
  val layoutManager = layoutManager as LinearLayoutManager? ?: return false
//    val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()
  val lastVisibleItemPosition = layoutManager.findLastCompletelyVisibleItemPosition()
  val visibleItemCount = layoutManager.childCount
  val totalItemCount = layoutManager.itemCount
  val state = scrollState
  return visibleItemCount > 0 && lastVisibleItemPosition == (totalItemCount - 1) &&
    state == RecyclerView.SCROLL_STATE_IDLE
}

/**
 * simulate own or parent horizontal layout click events
 */
@SuppressLint("ClickableViewAccessibility")
fun RecyclerView.simulateClickListener(parentClick: () -> Unit) {
  var isMove: Boolean = false
  var start = 0f
  this.setOnTouchListener { v, event ->
    if (event.action == MotionEvent.ACTION_DOWN) {
      start = event.x
      isMove = false
    }
    if (event.action == MotionEvent.ACTION_CANCEL ||
      event.action == MotionEvent.ACTION_MOVE
    ) {
      val diff = abs(event.x - start)
      if (diff > 1) {
        isMove = true
      }
    }
    if (event.action == MotionEvent.ACTION_UP && !isMove) {
      parentClick()
    }
    false
  }
}
