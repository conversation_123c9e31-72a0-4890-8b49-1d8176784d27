package com.ricepo.base.view

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.view.isVisible
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.ricepo.base.R
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.base.tools.ToastUtil
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 13/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object DialogFacade {

  private var alertDialog: AlertDialog? = null

  // the builder of show alert
  fun showDialog(params: DialogParams, isReset: Boolean = false) {
    val context = params.context ?: return
    val message = params.message ?: ResourcesUtil.getString(params.messageId)
    if (message.isEmpty()) return
    val title = params.title ?: ResourcesUtil.getString(params.titleId)
    val canCancel = params.canCancel
    val positiveId = params.positiveId
    val positive = params.positive
    val negativeId = params.negativeId
    val negative = params.negative

    val builder = MaterialAlertDialogBuilder(context, com.ricepo.style.R.style.AlertDialogTheme)

    builder
      .setTitle(title)
      .setMessage(message)
      .setCancelable(canCancel)

    builder.apply {
      setPositiveButton(positiveId) { dialog, id ->
        dismissDialog(context, dialog)
        positive()
      }
    }

    if (negativeId != null && negativeId > 0) {
      builder.setNegativeButton(negativeId) { dialog, which ->
        dismissDialog(context, dialog)
        negative()
      }
    }

    if (alertDialog == null || isReset) {
      dismissDialog(context, alertDialog)
      alertDialog = builder.create()
    } else if (alertDialog?.context != context && alertDialog != null) {
      // reset dialog if dialog already exists and context changed
      // if async show dialog and quickly dismiss that context changed
      alertDialog = null
      alertDialog = builder.create()
    }

    try {
      if (isValidContext(context)) {

        // show dialog
        alertDialog?.show()

        // cancel listener
        alertDialog?.setOnCancelListener { it ->
          dismissDialog(context, it)
          negative()
        }

        // adapter android 9.0
        adapterWindow(alertDialog)
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  /**
   * check the activity status
   */
  private fun isValidContext(context: Context): Boolean {
    return (context is Activity) && !context.isFinishing && !context.isDestroyed
  }

  /**
   * dismiss dialog
   */
  private fun dismissDialog(context: Context, dialog: DialogInterface?) {
    if (isValidContext(context) && alertDialog?.isShowing == true) {
      try {
        dialog?.dismiss()
      } catch (e: Exception) {
        // not attached to window manager
        e.printStackTrace()
      }
    }
    alertDialog = null
  }

  /**
   * adapter android 9.0 window
   */
  private fun adapterWindow(dialog: AlertDialog?) {
    if (dialog != null) {
      val dialogWindow = dialog.window ?: return
      val windowParams = dialogWindow.attributes
      val width = DisplayUtil.getScreenWidth()
      windowParams.width = (width * 0.95).toInt()
      windowParams.height = WindowManager.LayoutParams.WRAP_CONTENT
      windowParams.gravity = Gravity.CENTER
      dialogWindow.attributes = windowParams
    }
  }

  fun showAlert(
    context: Context,
    message: String?,
    title: String? = null,
    canCancel: Boolean = false,
    isReset: Boolean = false,
    positiveId: Int = com.ricepo.style.R.string.ok,
    positive: () -> Unit = {}
  ) {
    val params = DialogParams(
      context = context, message = message,
      title = title,
      canCancel = canCancel,
      positiveId = positiveId, positive = positive
    )
    showDialog(params, isReset)
  }

  fun showAlert(
    context: Context,
    messageId: Int,
    titleId: Int = com.ricepo.style.R.string.ricepo,
    canCancel: Boolean = false,
    isReset: Boolean = false,
    positiveId: Int = com.ricepo.style.R.string.ok,
    positive: () -> Unit = {}
  ) {
    val params = DialogParams(
      context = context, titleId = titleId, messageId = messageId,
      canCancel = canCancel, positiveId = positiveId, positive = positive
    )
    showDialog(params, isReset)
  }

  fun showPrompt(
    context: Context,
    messageId: Int,
    titleId: Int = com.ricepo.style.R.string.ricepo,
    negativeId: Int? = com.ricepo.style.R.string.cancel,
    negative: () -> Unit = {},
    positiveId: Int = com.ricepo.style.R.string.ok,
    positive: () -> Unit = {}
  ) {
    val params = DialogParams(
      context = context, titleId = titleId, messageId = messageId,
      positiveId = positiveId, positive = positive, negativeId = negativeId, negative = negative
    )
    showDialog(params)
  }

  fun showPrompt(
    context: Context,
    message: String,
    title: String = ResourcesUtil.getString(com.ricepo.style.R.string.ricepo),
    negativeId: Int? = com.ricepo.style.R.string.cancel,
    negative: () -> Unit = {},
    positiveId: Int = com.ricepo.style.R.string.ok,
    positive: () -> Unit = {}
  ) {
    val params = DialogParams(
      context = context, title = title, message = message,
      positiveId = positiveId, positive = positive, negativeId = negativeId, negative = negative
    )
    showDialog(params)
  }

  /**
   * show the input dialog
   */
  fun showInput(
    context: Context,
    titleId: Int,
    messageId: Int? = null,
    hintId: Int,
    cancel: () -> Unit = {},
    confirm: (String) -> Unit
  ) {
    val title = ResourcesUtil.getString(titleId)
    val message = messageId?.let {
      ResourcesUtil.getString(it)
    }
    val hint = hintId.let {
      ResourcesUtil.getString(it)
    }
    showInput(context, title, message, hint, cancel, confirm)
  }

  /**
   * show the input dialog
   */
  fun showInput(
    context: Context,
    title: String?,
    message: String? = null,
    hint: String? = null,
    cancel: () -> Unit = {},
    confirm: (String) -> Unit
  ) {
    val builder = MaterialAlertDialogBuilder(context, com.ricepo.style.R.style.AlertDialogTheme)

    builder.setCancelable(false)

    val view = LayoutInflater.from(context).inflate(R.layout.layout_dialog_input, null)
    val tvDialogTitle = view.findViewById<TextView>(R.id.tv_dialog_title)
    val tvDialogMessage = view.findViewById<TextView>(R.id.tv_dialog_message)
    val etDialogInput = view.findViewById<EditText>(R.id.et_dialog_input)
    tvDialogTitle.isVisible = (title != null)
    title?.let {
      tvDialogTitle.text = it
    }
    if (message != null) {
      tvDialogMessage.text = message
      tvDialogMessage.visibility = View.VISIBLE
    } else {
      tvDialogMessage.visibility = View.GONE
    }

    etDialogInput.hint = hint
    builder.setView(view)

    builder.apply {
      setPositiveButton(com.ricepo.style.R.string.ok, null)
      setNegativeButton(com.ricepo.style.R.string.cancel) { dialog, id ->
        dialog.dismiss()
        cancel()
      }
    }

    val dialog = builder.create()
    dialog.setOnShowListener {
      val btnPositive = dialog.getButton(DialogInterface.BUTTON_POSITIVE)
      btnPositive.setOnClickListener {
        val content = etDialogInput.text.toString()
        if (content.trim().isNotEmpty()) {
          dialog.dismiss()
          confirm(content)
        } else {
          ToastUtil.showToast(etDialogInput.hint.toString())
        }
      }
    }

    dialog.setOnCancelListener {
      // hide input keyboard
      KeyboardUtil.hideKeyboard(context, etDialogInput)
    }

    dialog.show()
    adapterWindow(dialog)
    // show input keyboard
    KeyboardUtil.showKeyboard(context, etDialogInput)
  }
}

data class DialogParams(
  val context: Context?,
  val titleId: Int = com.ricepo.style.R.string.ricepo,
  val title: String? = null,
  val messageId: Int = com.ricepo.style.R.string.ricepo,
  val message: String? = null,
  val canCancel: Boolean = false,
  val positiveId: Int = com.ricepo.style.R.string.confirm,
  val positive: () -> Unit = {},
  val negativeId: Int? = null,
  val negative: () -> Unit = {}
)
