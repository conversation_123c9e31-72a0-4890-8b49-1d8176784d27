package com.ricepo.base.data.common

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import com.ricepo.base.data.common.kv.KeyValueDao
import com.ricepo.base.data.common.kv.KeyValueEntity

//
// Created by <PERSON><PERSON> on 20/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Database(entities = [KeyValueEntity::class], version = 2, exportSchema = false)
abstract class CommonDatabase : RoomDatabase() {

  /**
   * the key value data access object
   */
  abstract fun keyValueDao(): KeyValueDao

  companion object {
    private const val DATABASE_NAME = "common.db"

    @Volatile private var INSTANCE: CommonDatabase? = null

    fun getInstance(context: Context): CommonDatabase = INSTANCE ?: synchronized(this) {
      INSTANCE ?: buildDatabase(context).also { INSTANCE = it }
    }

    private fun buildDatabase(context: Context) =
      Room.databaseBuilder(
        context.applicationContext,
        CommonDatabase::class.java, DATABASE_NAME
      )
        .addCallback(object : RoomDatabase.Callback() {
          override fun onCreate(db: SupportSQLiteDatabase) {
            super.onCreate(db)
          }
        })
        .addMigrations(MigrationConst.MIGRATION_1_2)
        .build()
  }
}
