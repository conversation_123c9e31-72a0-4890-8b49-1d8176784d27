package com.ricepo.base.data.converter

import androidx.room.TypeConverter
import java.util.Date

//
// Created by <PERSON><PERSON> on 13/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 *  convert date type of database
 */
class DateConverter {

  @TypeConverter
  fun toDate(timestamp: Long?): Date? {
    return timestamp?.let { Date(it) }
  }

  @TypeConverter
  fun toTimestamp(date: Date?): Long? {
    return if (date == null) {
      null
    } else date.getTime()
  }
}
