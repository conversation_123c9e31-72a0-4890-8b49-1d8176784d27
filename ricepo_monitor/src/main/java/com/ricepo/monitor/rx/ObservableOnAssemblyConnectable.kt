/*
 * Copyright 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ricepo.monitor.rx

import com.ricepo.monitor.rx.ObservableOnAssembly.OnAssemblyObserver
import io.reactivex.rxjava3.core.Observer
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.functions.Consumer
import io.reactivex.rxjava3.observables.ConnectableObservable

/**
 * Wraps a ObservableSource and inject the assembly info.
 *
 * @param <T> the value type
</T> */
internal class ObservableOnAssemblyConnectable<T : Any>(val source: ConnectableObservable<T>) :
  ConnectableObservable<T>() {
  val assembled: RxJavaAssemblyException
  override fun subscribeActual(observer: Observer<in T>) {
    source.subscribe(OnAssemblyObserver(observer, assembled))
  }

  override fun connect(connection: Consumer<in Disposable>) {
    source.connect(connection)
  }

  override fun reset() {
    source.reset()
  }

  init {
    assembled = RxJavaAssemblyException()
  }
}
