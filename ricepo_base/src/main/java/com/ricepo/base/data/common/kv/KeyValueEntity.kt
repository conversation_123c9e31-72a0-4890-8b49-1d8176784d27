package com.ricepo.base.data.common.kv

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.ricepo.base.data.converter.DateConverter
import java.util.Date

//
// Created by <PERSON><PERSON> on 20/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Entity(tableName = "t_value")
data class KeyValueEntity(

  @PrimaryKey
  @ColumnInfo(name = "f_key")
  var key: String = "default",

  @ColumnInfo(name = "f_value")
  var value: String? = null
) {

  @ColumnInfo(name = "updated_at", defaultValue = "CURRENT_TIMESTAMP")
  @TypeConverters(DateConverter::class)
  private var updatedAt: Date? = null

  open fun getUpdatedAt(): Date? {
    return updatedAt
  }

  open fun setUpdatedAt(updatedAt: Date?) {
    this.updatedAt = updatedAt
  }
}
