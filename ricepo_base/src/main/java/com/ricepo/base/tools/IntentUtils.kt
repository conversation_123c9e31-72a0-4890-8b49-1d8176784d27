package com.ricepo.base.tools

import android.Manifest
import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ComponentName
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.FileProvider
import androidx.core.os.EnvironmentCompat
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.lifecycleScope
import com.eazypermissions.common.model.PermissionResult
import com.eazypermissions.coroutinespermission.PermissionManager
import com.ricepo.base.R
import com.ricepo.base.consts.BaseConstant
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

//
// Created by <PERSON><PERSON> on 3/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object IntentUtils {

  /**
   * send emial to feedback
   */
  fun intentMail(context: Context, file: File?) {
    val title = ResourcesUtil.getString(com.ricepo.style.R.string.feedback)
    Uri.parse("mailto:${BaseConstant.FEEDBACK_EMAIL}")
    val email = arrayOf(BaseConstant.FEEDBACK_EMAIL)
    val intent = Intent(Intent.ACTION_SEND)
    intent.putExtra(Intent.EXTRA_EMAIL, email)
    intent.putExtra(Intent.EXTRA_SUBJECT, title)
    intent.type = "text/plain"
    if (file != null && file.exists()) {
      try {
        val uri = FileProvider.getUriForFile(
          context,
          context.applicationContext.packageName + ".fileprovider", file
        )
        intent.putExtra(Intent.EXTRA_STREAM, uri)
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
    context.startActivity(
      Intent.createChooser(
        intent,
        title
      )
    )
  }

  /**
   * navigate to Google Map
   */
  fun intentGoogleMap(context: Context, lat: String, lon: String, address: String? = null) {
    // Creates an Intent that will load a map of lat,lon
    val uri = if (address != null) {
      "geo:$lat,$lon?q=${Uri.encode(address)}"
    } else {
      "geo:$lat,$lon"
    }

    val gmmIntentUri = Uri.parse(uri)
    val mapIntent = Intent(Intent.ACTION_VIEW, gmmIntentUri)
    mapIntent.setPackage("com.google.android.apps.maps")
    val gmApp = mapIntent.resolveActivity(context.packageManager)
    if (gmApp != null) {
      context.startActivity(mapIntent)
    } else {
      val baseMapUrl = StringBuilder("https://www.google.com/maps/search/?api=1&query=")
      val url = if (address != null) {
        baseMapUrl.append(Uri.encode(address)).toString()
      } else {
        baseMapUrl.append("$lat,$lon").toString()
      }
      intentBrowser(context, url)
    }
  }

  /**
   * navigate with browser
   */
  fun intentBrowser(context: Context, url: String?) {
    val intent = Intent()
    intent.action = Intent.ACTION_VIEW
    intent.data = Uri.parse(url)
    // Name of the component implementing an activity that can display the intent
    if (intent.resolveActivity(context.packageManager) != null) {
      val componentName: ComponentName = intent.resolveActivity(context.packageManager)
      context.startActivity(Intent.createChooser(intent, "Please select a browser"))
    } else {
      // show toast error
    }
  }

  /**
   * intent phone call with coroutines permission
   */
  fun intentPhone(context: Context, lifecycleScope: LifecycleCoroutineScope, phoneNumber: String) {
    if (context is AppCompatActivity) else return
    val activity = context as AppCompatActivity
    val REQUEST_ID: Int = 0x222

    lifecycleScope.launch {
      // CoroutineScope suspends the coroutine
      val permissionResult = PermissionManager.requestPermissions(
        activity,
        REQUEST_ID,
        Manifest.permission.CALL_PHONE
      )

      // Resume coroutine once result is ready
      when (permissionResult) {
        is PermissionResult.PermissionGranted -> {
          // Add your logic here after user grants permission(s)
          navigatePhone(activity, phoneNumber)
        }
        is PermissionResult.PermissionDenied -> {
          // Add your logic to handle permission denial
        }
        is PermissionResult.PermissionDeniedPermanently -> {
          // Add your logic here if user denied permission(s) permanently.
          // Ideally you should ask user to manually go to settings and enable permission(s)
          DialogFacade.showAlert(context, com.ricepo.style.R.string.permission_check_phone)
        }
        is PermissionResult.ShowRational -> {
          // If user denied permission frequently then she/he is not clear about why you are asking this permission.
          // This is your chance to explain them why you need permission.
        }
      }
    }
  }

  /**
   * navigate with phone call
   * need CALL_PHONE permission and Runtime permission
   */
  private fun navigatePhone(context: Context, phoneNumber: String) {
    val intent = Intent(Intent.ACTION_CALL)
    val data = Uri.parse("tel: ${phoneNumber.replace(" ", "")}")
    intent.data = data
    if (ActivityCompat.checkSelfPermission(
        context,
        Manifest.permission.CALL_PHONE
      )
      != PackageManager.PERMISSION_GRANTED
    ) {
      return
    }
    context.startActivity(intent)
  }

  /**
   * intent camera with coroutines permission
   */
  fun intentCamera(
    context: Context,
    requestCode: Int,
    block: (imagePath: String?, imageUri: Uri?) -> Unit
  ) {
    if (context is AppCompatActivity) else return
    val activity = context
    val REQUEST_ID: Int = 0x223

    val lifecycleScope = context.lifecycleScope
    lifecycleScope.launch {
      // CoroutineScope suspends the coroutine

      var permissionResult = PermissionManager.requestPermissions(
        activity,
        REQUEST_ID,
        Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE
      )

//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//                permissionResult = PermissionManager.requestPermissions(
//                    activity,
//                    REQUEST_ID,
//                    Manifest.permission.CAMERA
//                )
//            }

      // Resume coroutine once result is ready
      when (permissionResult) {
        is PermissionResult.PermissionGranted -> {
          // Add your logic here after user grants permission(s)
          navigateCamera(activity, requestCode, block)
        }
        is PermissionResult.PermissionDenied -> {
          // Add your logic to handle permission denial
        }
        is PermissionResult.PermissionDeniedPermanently -> {
          // Add your logic here if user denied permission(s) permanently.
          // Ideally you should ask user to manually go to settings and enable permission(s)
          DialogFacade.showAlert(context, com.ricepo.style.R.string.permission_check_camera)
        }
        is PermissionResult.ShowRational -> {
          // If user denied permission frequently then she/he is not clear about why you are asking this permission.
          // This is your chance to explain them why you need permission.
        }
      }
    }
  }

  /**
   * get the image chooser intent
   */
  fun getImageIntent(context: Context): Triple<Intent, Uri?, String?>? {
    val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
    // determine if there is a camera
    return if (intent.resolveActivity(context.packageManager) != null) {
      var photoFile: File? = null
      var photoUri: Uri? = null
      var photoPath: String? = null
      // Build.VERSION_CODES.Q
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        photoUri = createImageUri(context)
      } else {
        photoFile = createImageFile(context)
      }

      if (photoFile != null) {
        photoPath = photoFile.absolutePath
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
          photoUri = FileProvider.getUriForFile(
            context,
            context.packageName + ".fileprovider",
            photoFile
          )
        } else {
          photoUri = Uri.fromFile(photoFile)
        }
      }

      if (photoUri != null && context is AppCompatActivity) {
        intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri)
        intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
      }
      Triple(intent, photoUri, photoPath)
    } else {
      null
    }
  }

  /**
   * navigate with camera
   */
  private fun navigateCamera(
    context: Context,
    requestCode: Int,
    block: (imagePath: String?, imageUri: Uri?) -> Unit
  ) {
    val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
    // determine if there is a camera
    if (intent.resolveActivity(context.packageManager) != null) {
      var photoFile: File? = null
      var photoUri: Uri? = null
      // Build.VERSION_CODES.Q
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        photoUri = createImageUri(context)
      } else {
        photoFile = createImageFile(context)
      }

      var imagePath: String? = null
      if (photoFile != null) {
        imagePath = photoFile.absolutePath
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
          photoUri = FileProvider.getUriForFile(
            context,
            context.packageName + ".fileprovider",
            photoFile
          )
        } else {
          photoUri = Uri.fromFile(photoFile)
        }
      }

      if (photoUri != null && context is AppCompatActivity) {
        block(imagePath, photoUri)
        intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri)
        intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        context.startActivityForResult(intent, requestCode)
      }
    }
  }

  private fun createImageFile(context: Context): File? {
    val imageName = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
      .format(Calendar.getInstance().time)
    val storageDir = context.getExternalFilesDir((Environment.DIRECTORY_PICTURES)) ?: return null
    if (!storageDir.exists()) {
      storageDir.mkdirs()
    }
    val tempFile = File(storageDir, imageName)
    if (Environment.MEDIA_MOUNTED != EnvironmentCompat.getStorageState(tempFile)) {
      return null
    }
    return tempFile
  }

  private fun createImageUri(context: Context): Uri? {
    val status = Environment.getExternalStorageState()
    return if (status == Environment.MEDIA_MOUNTED) {
      context.contentResolver.insert(
        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
        ContentValues()
      )
    } else {
      context.contentResolver.insert(
        MediaStore.Images.Media.INTERNAL_CONTENT_URI,
        ContentValues()
      )
    }
  }

  /**
   * intent album with coroutines permission
   */
  fun intentAlbum(context: Context, requestCode: Int) {
    if (context is AppCompatActivity) else return
    val activity = context
    val REQUEST_ID: Int = 0x224

//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//            navigateAlbum(activity, requestCode)
//            return
//        }

    val lifecycleScope = context.lifecycleScope
    lifecycleScope.launch {
      // CoroutineScope suspends the coroutine
      val permissionResult = PermissionManager.requestPermissions(
        activity,
        REQUEST_ID,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
      )

      // Resume coroutine once result is ready
      when (permissionResult) {
        is PermissionResult.PermissionGranted -> {
          // Add your logic here after user grants permission(s)
          navigateAlbum(activity, requestCode)
        }
        is PermissionResult.PermissionDenied -> {
          // Add your logic to handle permission denial
        }
        is PermissionResult.PermissionDeniedPermanently -> {
          // Add your logic here if user denied permission(s) permanently.
          // Ideally you should ask user to manually go to settings and enable permission(s)
          DialogFacade.showAlert(context, com.ricepo.style.R.string.permission_check_storage)
        }
        is PermissionResult.ShowRational -> {
          // If user denied permission frequently then she/he is not clear about why you are asking this permission.
          // This is your chance to explain them why you need permission.
        }
      }
    }
  }

  /**
   * navigate with album
   */
  private fun navigateAlbum(context: Context, requestCode: Int) {
    var intent = Intent()
    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
      intent.action = Intent.ACTION_GET_CONTENT
      intent.type = "image/*"
    } else {
      intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
      intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*")
    }
    if (context is AppCompatActivity) {
      context.startActivityForResult(intent, requestCode)
    }
  }

  /**
   * media uri: content://
   * other filer uri: file://
   * parse the image uri or path to path
   */
  fun parseImagePath(context: Context, imageUri: Uri? = null, imagePath: String? = null): String? {
    val filePathColumns = arrayOf<String>(MediaStore.Images.Media.DATA)
    var path = imagePath
    if (imageUri != null && path == null) {
      val cursor = context.contentResolver.query(imageUri, filePathColumns, null, null, null)
      if (cursor != null) {
        cursor.moveToFirst()
        val columnIndex = cursor.getColumnIndex(filePathColumns.first())
        path = cursor.getString(columnIndex)
      }
    }
    return path
  }

  /**
   * share content with others app
   */
  fun shareToOthers(context: Context, text: String) {
    val sendIntent: Intent = Intent().apply {
      action = Intent.ACTION_SEND
      putExtra(Intent.EXTRA_TEXT, text)
      type = "text/plain"
    }

    val shareIntent = Intent.createChooser(sendIntent, null)
    context.startActivity(shareIntent)
  }

  /**
   * share content with clipboard
   */
  fun shareWithClip(context: Context, content: String) {
    val cm = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clipData = ClipData.newPlainText("label", content)
    cm.setPrimaryClip(clipData)
  }

  /**
   * back the desktop screen
   */
  fun backHome(activity: Activity) {
    val intent = Intent(Intent.ACTION_MAIN)
    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
    intent.addCategory(Intent.CATEGORY_HOME)
    activity.startActivity(intent)
  }
}
