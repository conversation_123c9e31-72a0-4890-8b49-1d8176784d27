package com.ricepo.base.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 17/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * restaurant banners model
 */
@Parcelize
data class RegionModel(
  @SerializedName("_id")
  val id: String?,
  val name: String?,
  val timezone: String?,
  val tax: Double?,
  val location: RegionLocation?,
  val geometry: RegionGeometry?,
  val stats: RegionStats?,
  val updatedAt: String?,
  @SerializedName("__v")
  val v: Int?,
//    val tron: TRON?,
  val motd: InternationalizationContent?,
  val banners: List<Banner>?
) : Parcelable

/*
 * Coordinated of region geometry may be three or four dimensional array
 * so construct enum value for it
 */
sealed class RegionGeometryCoordinates {
  data class threeDimensionalArray(val v1: List<List<List<Double>>>) : RegionGeometryCoordinates()
  data class fourDimensionalArray(val v1: List<List<List<List<Double>>>>) : RegionGeometryCoordinates()

  // constructor three and four dimensional
}

// MARK: - Location
@Parcelize
data class RegionLocation(
  val type: String,
  val coordinates: List<Double>
) : Parcelable

// MARK: - Stats
@Parcelize
data class RegionStats(val delivery: RegionDelivery?) : Parcelable

// MARK: - Delivery
@Parcelize
data class RegionDelivery(
  val avg: Double?,
  val std: Double?,
  val hourly: List<Double>
) : Parcelable

// MARK: - TRON
data class TRON(val provider: String?)

// MARK: - TRON
@Parcelize
data class Banner(
  // / Banner large title
  val title: InternationalizationContent?,
  val subtitle: InternationalizationContent?,
  // / Banner sub title
  val button: InternationalizationContent?,
  // / Banner image
  val image: ThemeImage?,
  // / Determine the action after clicking based on type
  var type: BannerType? = null,
  // / Open web with url
  val url: String?,
  // / Data for plan
  val data: BannerData?
) : Parcelable

@Parcelize
data class ThemeImage(
  val light: String?,
  val dark: String?
) : Parcelable

// / Determine the action after clicking based on type
enum class BannerType(val rawValue: String) {
  web("web"), refer("refer"), rank("rank"), restaurant("restaurant"),
  recommendation("recommendation"), plan("plan"), unknow("unknow");

  companion object {
    operator fun invoke(rawValue: String) = BannerType.values().firstOrNull { it.rawValue == rawValue }
  }
}

// / Open a web in app
// / Share
// / Rank
// / Recommendation
// / Plan
// / Unknow if do not support
// / Data in banner
@Parcelize
data class BannerData(
  val plan: SubscriptionPlan?,
  val restaurantId: String?,
) : Parcelable
