package com.ricepo.base.analytics

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.ricepo.base.R
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Customer
import com.ricepo.base.model.Food
import com.ricepo.monitor.firebase.FirebaseEvent
import com.ricepo.monitor.firebase.FirebaseMenuEvent
import com.ricepo.monitor.firebase.FirebaseMonitor
import com.ricepo.monitor.firebase.FirebaseRestaurantEvent
import java.text.SimpleDateFormat

//
// Created by <PERSON><PERSON> on 22/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object AnalyticsFacade {

  /**
   * upload user property when:
   * 1. login & get customer data
   * 2. order state is [“confirmed”,“created”,“sent”] & get customer data
   * 3. open app every time & get customer data
   */
  fun updateUserProp(customer: Customer?) {
    val user = customer ?: return

    setUserProp("orderCount", user.orderCount?.toString())
    setUserProp("region__id", user.region?.id)
    setUserProp("subscription_status", user.subscription?.status)
    setUserProp("createdAt", formatDate(user.createdAt))
    setUserProp("language", user.language)
    setUserProp("lastOrder_createdAt", user.lastOrder?.createdAt)
  }

  private fun formatDate(dateStr: String?): String? {
    val date = dateStr ?: return null
    val format = SimpleDateFormat("yyyyMm")
    return try {
      format.format(format.parse(date))
    } catch (e: Exception) {
      null
    }
  }

  /**
   * set user property to define audiences
   */
  private fun setUserProp(key: String, value: String?) {
    Firebase.analytics.setUserProperty(key, value)
  }

  /**
   * monitor the list scroll
   */
  fun horizontalScrollRestaurant(recyclerView: RecyclerView?, eventName: String) {
    recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {

      var isDraggingOrFling = false

      override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        if (recyclerView.scrollState == RecyclerView.SCROLL_STATE_DRAGGING ||
          recyclerView.scrollState == RecyclerView.SCROLL_STATE_SETTLING
        ) {
          isDraggingOrFling = true
        }
      }

      override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
//                Log.i("thom", "scrollState = ${recyclerView.scrollState} newState = $newState")
        if (isDraggingOrFling && recyclerView.scrollState == RecyclerView.SCROLL_STATE_IDLE) {
          isDraggingOrFling = false
          val layoutManager = recyclerView.layoutManager
          if (layoutManager is LinearLayoutManager) {
            val pos = layoutManager.findFirstVisibleItemPosition()
            val view = layoutManager.findViewByPosition(pos)
            val event = view?.getTag(R.id.tag_firebase_event)
            if (event is FirebaseRestaurantEvent) {
              event.rScrollDepth = ScrollDepthFacade.scrollDepth
              FirebaseMonitor.logEvent(eventName, event)
            }
          }
        }
      }
    })
  }

  /**
   * monitor firebase event
   */
  fun logEvent(view: View, eventName: String) {
    val event = view?.getTag(R.id.tag_firebase_event)
    if (event is FirebaseEvent) {
      event.rScrollDepth = ScrollDepthFacade.scrollDepth
      FirebaseMonitor.logEvent(eventName, event)
    }
  }

  /**
   * monitor firebase event by food
   */
  fun logEvent(food: Food, eventName: String) {
    val event = FirebaseMenuEvent(
      rCategoryId = food.category?.id,
      rCategoryIndex = food.category?.categoryIndex?.toLong(),
      rCategoryType = food.category?.categoryType,
      rFoodIndex = food.category?.foodIndex?.toLong(),
      rScrollDepth = ScrollDepthFacade.scrollDepth
    )
    if (event is FirebaseEvent) {
      FirebaseMonitor.logEvent(eventName, event)
    }
  }

  /**
   * monitor firebase event by cart
   */
  fun logEvent(cart: Cart, cartIndex: Int, eventName: String) {
    val event = FirebaseMenuEvent(
      rCategoryId = cart.categoryId,
      rFoodIndex = cartIndex.toLong(),
      rScrollDepth = ScrollDepthFacade.scrollDepth
    )
    if (event is FirebaseEvent) {
      FirebaseMonitor.logEvent(eventName, event)
    }
  }

  /**
   * monitor firebase event
   */
  fun logEvent(event: FirebaseEvent, eventName: String) {
    if (event is FirebaseEvent) {
      FirebaseMonitor.logEvent(eventName, event)
    }
  }
}
