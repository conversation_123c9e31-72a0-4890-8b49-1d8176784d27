<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/colorPrimary"
    android:clickable="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_error_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="0.62"
        app:layout_constraintWidth_percent="0.8"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:src="@drawable/ic_error_no_network" />

    <TextView
        android:id="@+id/tv_error_title"
        app:layout_constraintTop_toBottomOf="@+id/iv_error_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:text="@string/error_title_load_failed"
        style="@style/TitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_error_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:maxLines="3"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_error_title" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_error_operator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:minWidth="150dp"
        android:gravity="center"
        style="@style/ButtonSecondaryStyle"
        app:layout_constraintTop_toBottomOf="@+id/tv_error_message"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>