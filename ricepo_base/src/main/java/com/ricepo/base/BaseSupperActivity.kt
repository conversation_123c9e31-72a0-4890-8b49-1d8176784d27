package com.ricepo.base

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration.UI_MODE_NIGHT_MASK
import android.content.res.Configuration.UI_MODE_NIGHT_YES
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.gyf.immersionbar.ImmersionBar
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.base.tools.ToastUtil
import com.ricepo.style.DisplayUtil
import com.ricepo.style.LocaleUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.ThemeUtil
import com.trello.rxlifecycle4.components.support.RxAppCompatActivity
import com.ricepo.base.R

object BaseEntrance {
  const val PARAM_PAGE_ENTRANCE = "page_entrance"
}

// CustomAdapt,
open class BaseSupperActivity : RxAppCompatActivity() {

  lateinit var firebaseAnalytics: FirebaseAnalytics

  var entrance: String? = null

  @SuppressLint("SourceLockedOrientationActivity")
  override fun onCreate(savedInstanceState: Bundle?) {

    firebaseAnalytics = Firebase.analytics

    super.onCreate(savedInstanceState)

    // reset the diplay density to default
    DisplayUtil.setDensity(resources)

    // the screen orientation
    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT

    // the entrance of page
    entrance = intent.getStringExtra(BaseEntrance.PARAM_PAGE_ENTRANCE)
  }

  fun fitStatusBar() {
    // system status bar configure
    val bar = ImmersionBar.with(this)
      .fitsSystemWindows(true)
      .statusBarColor(com.ricepo.style.R.color.colorPrimary)
    if (!ThemeUtil.isDarkMode()) {
      bar.statusBarDarkFont(true, 1f)
    }
    bar.init()
  }

  override fun onStart() {
    super.onStart()
    // setup after set content view
    setupBaseListener()
  }

  override fun onStop() {
    ToastUtil.cancel()
    super.onStop()
  }

  override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
    if (ev.action == MotionEvent.ACTION_DOWN) {
      val v = currentFocus
      // hide keyboard when touch outside of keyboard
      if (KeyboardUtil.isShouldHideKeyboard(v, ev)) {
        KeyboardUtil.hideKeyboard(this, v?.windowToken)
      }
    }
    return super.dispatchTouchEvent(ev)
  }

  open fun setTitle(title: String?) {
    findViewById<TextView>(R.id.tv_title)?.apply {
      text = title
    }
  }

  open fun setTitleSubText(title: String?) {
    findViewById<TextView>(R.id.tv_title)?.apply {
      text = title ?: ""
      setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, this))
    }
  }

  override fun setTitle(titleId: Int) {
    findViewById<TextView>(R.id.tv_title)?.apply {
      text = ResourcesUtil.getString(titleId)
    }
  }

  // listener the back and close action
  private fun setupBaseListener() {
    findViewById<View>(R.id.iv_back)?.setOnClickListener {
      onBackPressed()
    }
    findViewById<View>(R.id.iv_close)?.setOnClickListener {
      onBackPressed()
    }
  }

  // check android q+ dark mode
  private fun checkDartMode() {
    // judge dark mode and set theme
    if ((this.resources.configuration.uiMode and UI_MODE_NIGHT_MASK) == UI_MODE_NIGHT_YES) {
      setTheme(com.ricepo.style.R.style.AppThemeDark)
    } else {
      setTheme(com.ricepo.style.R.style.AppThemeLight)
    }
  }

  override fun attachBaseContext(newBase: Context) {
    super.attachBaseContext(LocaleUtil.setLocale(newBase))
  }

  /**
   * show error view
   */
  protected fun showErrorView(
    viewGroup: ViewGroup,
    iconId: Int,
    titleResId: Int,
    message: String?,
    btnResId: Int,
    isCleanWhenClick: Boolean = true,
    listener: View.OnClickListener? = null
  ) {
    val errorView = layoutInflater.inflate(R.layout.layout_error_container, null)
    viewGroup.addView(errorView)
    errorView.apply {
      this.findViewById<ImageView>(R.id.iv_error_icon)?.setImageResource(iconId)
      this.findViewById<TextView>(R.id.tv_error_title)?.text = ResourcesUtil.getString(titleResId)
      if (message != null) this.findViewById<TextView>(R.id.tv_error_message)?.text = message
      this.findViewById<TextView>(R.id.btn_error_operator)?.text = ResourcesUtil.getString(btnResId)
      this.findViewById<TextView>(R.id.btn_error_operator)?.setOnClickListener {
        if (isCleanWhenClick) {
          clearErrorView(viewGroup)
        }
        listener?.onClick(this)
      }
    }
  }

  /**
   * remove the error view
   */
  protected fun clearErrorView(viewGroup: ViewGroup) {
    viewGroup.removeAllViews()
  }

  open fun backEvent() {
    finish()
  }

  open fun backResultEvent(intent: Intent) {
    setResult(Activity.RESULT_OK, intent)
    backEvent()
  }
}

data class ErrorInput(
  val imageId: Int,
  val titleId: Int,
  val message: String?,
  val buttonId: Int,
  val isCleanWhenClick: Boolean = true,
  val click: () -> Unit
)
