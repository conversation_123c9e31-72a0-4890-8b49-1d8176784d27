package com.ricepo.base.tools

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.view.View
import com.ricepo.base.R
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.span.CenteredImageSpan

//
// Created by <PERSON><PERSON> on 23/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object StringUtils {

  fun convertCoinPlainToImage(view: View, content: String): SpannableStringBuilder {
    val range = content.indexOf("[coin]")
    val drawable = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_coin, view.context)
    drawable.setBounds(
      0, 0, ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_19dp),
      ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_18dp)
    )
    val imageSpan = CenteredImageSpan(drawable)
    val spanText = SpannableStringBuilder(content)
    try {
      spanText.setSpan(imageSpan, range, range + 6, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return spanText
  }
}

// Regular expression is too slow
fun String.compressSpace(): String {
  var result = this
  while (result.contains("  ")) {
    result = result.replace("  ", " ")
  }
  return result
}
