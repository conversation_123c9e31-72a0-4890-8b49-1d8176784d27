package com.ricepo.base.remoteconfig.base

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

interface KonfigModel

fun KonfigModel.konfig(key: String, default: Long): RemoteKonfigLongDelegate {
  return RemoteKonfigLongDelegate(key, default)
}

fun KonfigModel.konfig(key: String, default: Int): RemoteKonfigIntDelegate {
  return RemoteKonfigIntDelegate(key, default)
}

fun KonfigModel.konfig(key: String, default: String): RemoteKonfigStringDelegate {
  return RemoteKonfigStringDelegate(key, default)
}

fun KonfigModel.konfig(key: String, default: Double): RemoteKonfigDoubleDelegate {
  return RemoteKonfigDoubleDelegate(key, default)
}

fun KonfigModel.konfig(key: String, default: Boolean): RemoteKonfigBooleanDelegate {
  return RemoteKonfigBooleanDelegate(key, default)
}

abstract class RemoteKonfigDelegate<out T : Any>
internal constructor(private val key: String, private val default: T) : ReadOnlyProperty<KonfigModel, T> {

  operator fun provideDelegate(thisRef: KonfigModel, property: KProperty<*>): ReadOnlyProperty<KonfigModel, T> {
    RemoteKonfig.register(thisRef.javaClass, key, default)
    return this
  }
}

class RemoteKonfigBooleanDelegate internal constructor(private val key: String, default: Boolean) :
  ReadOnlyProperty<KonfigModel, Boolean>, RemoteKonfigDelegate<Boolean>(key, default) {

  override fun getValue(thisRef: KonfigModel, property: KProperty<*>): Boolean {
    return FirebaseRemoteConfig.getInstance().getBoolean(key)
  }
}

class RemoteKonfigIntDelegate internal constructor(private val key: String, default: Int) :
  ReadOnlyProperty<KonfigModel, Int>, RemoteKonfigDelegate<Int>(key, default) {

  override fun getValue(thisRef: KonfigModel, property: KProperty<*>): Int {
    return FirebaseRemoteConfig.getInstance().getLong(key).toInt()
  }
}

class RemoteKonfigLongDelegate internal constructor(private val key: String, default: Long) :
  ReadOnlyProperty<KonfigModel, Long>, RemoteKonfigDelegate<Long>(key, default) {

  override fun getValue(thisRef: KonfigModel, property: KProperty<*>): Long {
    return FirebaseRemoteConfig.getInstance().getLong(key)
  }
}

class RemoteKonfigDoubleDelegate internal constructor(private val key: String, default: Double) :
  ReadOnlyProperty<KonfigModel, Double>, RemoteKonfigDelegate<Double>(key, default) {

  override fun getValue(thisRef: KonfigModel, property: KProperty<*>): Double {
    return FirebaseRemoteConfig.getInstance().getDouble(key)
  }
}

class RemoteKonfigStringDelegate internal constructor(private val key: String, default: String) :
  ReadOnlyProperty<KonfigModel, String>, RemoteKonfigDelegate<String>(key, default) {

  override fun getValue(thisRef: KonfigModel, property: KProperty<*>): String {
    return FirebaseRemoteConfig.getInstance().getString(key)
  }
}
