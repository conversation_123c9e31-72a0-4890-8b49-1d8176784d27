package com.ricepo.network.converter

import com.google.gson.Gson
import com.google.gson.TypeAdapter
import com.google.gson.stream.JsonReader
import okhttp3.MediaType
import okhttp3.ResponseBody
import retrofit2.Converter
import java.io.IOException
import java.io.InputStream
import java.io.InputStreamReader
import java.io.Reader
import java.nio.charset.Charset

//
// Created by <PERSON><PERSON> on 27/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class GsonResponseBodyConverter<Any>(private val gson: Gson, private val adapter: TypeAdapter<out Any>) :
  Converter<ResponseBody, Any> {

  val UTF_8 = Charset.forName("UTF-8")

  @Throws(IOException::class)
  override fun convert(value: ResponseBody): Any? {

    val jsonValue = value.string()
    if (jsonValue.isNullOrEmpty()) {
      return null
    }
    return value.use { value ->
      val contentType: MediaType? = value.contentType()
      val charset: Charset = contentType?.charset(UTF_8) ?: UTF_8
      val inputStream: InputStream = jsonValue.byteInputStream(charset)
      val reader: Reader = InputStreamReader(inputStream, charset)
      val jsonReader: JsonReader = gson.newJsonReader(reader)
      adapter.read(jsonReader)
    }
  }
}
