package com.ricepo.base.analytics

import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.style.DisplayUtil

//
// Created by <PERSON><PERSON> on 17/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object ScrollDepthFacade {

  var scrollDepth = 0.0

  private var mTotalDy = 0f

  fun computeScrollDepth(recyclerView: RecyclerView?) {
    recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
      var isDragging = true
      init {
        scrollDepth = 0.0
        mTotalDy = 0f
      }

      override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        if (recyclerView.scrollState == RecyclerView.SCROLL_STATE_DRAGGING ||
          recyclerView.scrollState == RecyclerView.SCROLL_STATE_SETTLING
        ) {
          isDragging = true
          mTotalDy += dy
        }
      }

      override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        if (newState == RecyclerView.SCROLL_STATE_IDLE && isDragging) {
          isDragging = false
          calcScrollDepth(recyclerView)
        }
      }
    })
  }

  fun resumeScrollDepth(recyclerView: RecyclerView?) {
    calcScrollDepth(recyclerView)
  }

  private fun calcScrollDepth(recyclerView: RecyclerView?) {
    val layoutManager = recyclerView?.layoutManager
    if (layoutManager is LinearLayoutManager) {
      val screenHeight = DisplayUtil.getContentHeight()
      val depth = mTotalDy
      scrollDepth = depth.div(screenHeight).toDouble()
    }
  }

  fun computeScrollDepth(scrollView: NestedScrollView?) {
    scrollView?.setOnScrollChangeListener(object : NestedScrollView.OnScrollChangeListener {

      val screenHeight = DisplayUtil.getScreenHeight()

      init {
        scrollDepth = 0.0
      }

      override fun onScrollChange(
        v: NestedScrollView,
        scrollX: Int,
        scrollY: Int,
        oldScrollX: Int,
        oldScrollY: Int
      ) {
        scrollDepth = scrollY.toDouble().div(screenHeight)
      }
    })
  }
}
