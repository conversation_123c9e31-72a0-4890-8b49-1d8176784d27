package com.ricepo.network.adapter

import com.ricepo.network.adapter.rx.Rx3CallAdapter
import hu.akarnokd.rxjava3.retrofit.Result
import io.reactivex.rxjava3.annotations.Nullable
import io.reactivex.rxjava3.core.Completable
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Maybe
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Scheduler
import io.reactivex.rxjava3.core.Single
import retrofit2.CallAdapter
import retrofit2.Response
import retrofit2.Retrofit
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 25/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class Rx3ErrorCallAdapterFactory private constructor(
  private val scheduler: @Nullable Scheduler?,
  private val isAsync: Boolean
) :
  CallAdapter.Factory() {
  override fun get(
    returnType: Type,
    annotations: Array<Annotation>,
    retrofit: Retrofit
  ): @Nullable CallAdapter<*, *>? {
    val rawType = getRawType(returnType)
    if (rawType == Completable::class.java) {
      // Completable is not parameterized (which is what the rest of this method deals with) so it
      // can only be created with a single configuration.
      return Rx3CallAdapter<Any>(
        Void::class.java, scheduler, isAsync, false, true, false, false,
        false, true
      )
    }
    val isFlowable = rawType == Flowable::class.java
    val isSingle = rawType == Single::class.java
    val isMaybe = rawType == Maybe::class.java
    if (rawType != Observable::class.java && !isFlowable && !isSingle && !isMaybe) {
      return null
    }
    var isResult = false
    var isBody = false
    val responseType: Type
    if (returnType !is ParameterizedType) {
      val name =
        if (isFlowable) "Flowable" else if (isSingle) "Single" else if (isMaybe) "Maybe" else "Observable"
      throw IllegalStateException(
        name + " return type must be parameterized" +
          " as " + name + "<Foo> or " + name + "<? extends Foo>"
      )
    }
    val observableType =
      getParameterUpperBound(0, returnType)
    val rawObservableType = getRawType(observableType)
    if (rawObservableType == Response::class.java) {
      check(observableType is ParameterizedType) {
        (
          "Response must be parameterized" +
            " as Response<Foo> or Response<? extends Foo>"
          )
      }
      responseType = getParameterUpperBound(
        0,
        observableType
      )
    } else if (rawObservableType == Result::class.java) {
      check(observableType is ParameterizedType) {
        (
          "Result must be parameterized" +
            " as Result<Foo> or Result<? extends Foo>"
          )
      }
      responseType = getParameterUpperBound(
        0,
        observableType
      )
      isResult = true
    } else {
      responseType = observableType
      isBody = true
    }
    return Rx3CallAdapter<Any>(
      responseType, scheduler, isAsync, isResult, isBody, isFlowable,
      isSingle, isMaybe, false
    )
  }

  companion object {
    /**
     * Returns an instance which creates synchronous observables that do not operate on any scheduler
     * by default.
     * @return the new adapter instance
     */
    fun create(): Rx3ErrorCallAdapterFactory {
      return Rx3ErrorCallAdapterFactory(null, false)
    }

    /**
     * Returns an instance which creates asynchronous observables. Applying
     * [Observable.subscribeOn] has no effect on stream types created by this factory.
     * @return the new adapter instance
     */
    fun createAsync(): Rx3ErrorCallAdapterFactory {
      return Rx3ErrorCallAdapterFactory(null, true)
    }

    /**
     * Returns an instance which creates synchronous observables that
     * [subscribe on][Observable.subscribeOn] `scheduler` by default.
     * @param scheduler the scheduler to run the network operations on
     * @return the new adapter instance
     */
    fun createWithScheduler(scheduler: Scheduler?): Rx3ErrorCallAdapterFactory {
      if (scheduler == null) throw NullPointerException("scheduler == null")
      return Rx3ErrorCallAdapterFactory(scheduler, false)
    }
  }
}
