package com.ricepo.base.lifecycle.weak

import kotlinx.coroutines.CancellationException
import java.lang.ref.WeakReference
import kotlin.coroutines.suspendCoroutine

//
// Created by Thomsen on 31/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class WeakRef<T> internal constructor(any: T) {
  private val weakRef = WeakReference(any)

  fun get(): T {
    return weakRef.get() ?: throw CancellationException("WeakRef cancel")
  }

  suspend operator fun invoke(): T {
    return suspendCoroutine {
      val ref = weakRef.get() ?: print("weakRef cancellation exception")
      ref
    }
  }
}

fun <T : Any> T.weakReference() = WeakRef(this)
