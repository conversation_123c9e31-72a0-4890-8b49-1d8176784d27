package com.ricepo.base.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 10/8/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

@Parcelize
data class CategoryJumpData(
  var groupId: String? = null,
  var itemPosition: Int? = null,
  var title: String? = null,
  var items: List<CategoryJumpItem>? = null,
  // restaurant sort title
  var sort: RestaurantSort? = null,
  val sortGroupId: String? = null,
  val sortSelectedId: String? = null
) : Parcelable

@Parcelize
data class CategoryJumpItem(
  val name: String? = null,
  val image: String? = null,
  val available: Boolean? = null,
) : Parcelable
