/*
 * Copyright 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ricepo.monitor.rx

import com.ricepo.monitor.rx.FlowableOnAssembly.OnAssemblyConditionalSubscriber
import com.ricepo.monitor.rx.FlowableOnAssembly.OnAssemblySubscriber
import io.reactivex.rxjava3.internal.fuseable.ConditionalSubscriber
import io.reactivex.rxjava3.parallel.ParallelFlowable
import org.reactivestreams.Subscriber

/**
 * Wrap a Parallel Flowable and inject the assembly info.
 *
 * @param <T> the value type
 * @since 0.15.2
</T> */
internal class ParallelFlowableOnAssembly<T : Any>(val source: ParallelFlowable<T>) :
  ParallelFlowable<T>() {
  val assembled: RxJavaAssemblyException
  override fun parallelism(): Int {
    return source.parallelism()
  }

  override fun subscribe(s: Array<Subscriber<in T>>) {
    if (validate(s)) {
      val n = s.size
      val parents: Array<Subscriber<in T>> = arrayOf()
      for (i in 0 until n) {
        val z = s[i]
        if (z is ConditionalSubscriber<*>) {
          parents[i] = OnAssemblyConditionalSubscriber(
            z as ConditionalSubscriber<in T>,
            assembled
          )
        } else {
          parents[i] = OnAssemblySubscriber(z, assembled)
        }
      }
      source.subscribe(parents)
    }
  }

  init {
    assembled = RxJavaAssemblyException()
  }
}
