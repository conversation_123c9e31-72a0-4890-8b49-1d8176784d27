<?xml version="1.0" encoding="utf-8"?>
<network-security-config xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

<!--    <domain-config>-->
<!--        <domain includeSubdomains="true">ricepo.com</domain>-->
<!--        <trust-anchors>-->
<!--            <certificates src="system" />-->
<!--        </trust-anchors>-->
<!--    </domain-config>-->

    <debug-overrides>
        <trust-anchors>
            <certificates src="@raw/my_ca"
                tools:ignore="NetworkSecurityConfig" />
        </trust-anchors>
    </debug-overrides>

    <!-- CLEARTEXT communication to to xx.xx.xx.xxx not permitted by network security policy -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system" overridePins="true" />
            <certificates src="user" overridePins="true" />
        </trust-anchors>
    </base-config>


</network-security-config>