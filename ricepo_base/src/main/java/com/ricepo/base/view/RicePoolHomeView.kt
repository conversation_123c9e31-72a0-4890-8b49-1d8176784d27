package com.ricepo.base.view

import android.content.Context
import android.os.CountDownTimer
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.view.isVisible
import com.ricepo.base.R
import com.ricepo.base.databinding.LayoutRicePoolHomeBinding
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import java.lang.Exception
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.TimeZone
import kotlin.math.ceil

//
// Created by <PERSON><PERSON> on 12/17/20.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class RicePoolHomeView : FrameLayout {

  constructor(context: Context) : super(context) {
    init(context, null)
  }

  constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
    init(context, attrs)
  }

  constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
    context,
    attrs,
    defStyleAttr
  ) {
    init(context, attrs)
  }

  constructor(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int,
    defStyleRes: Int
  ) : super(context, attrs, defStyleAttr, defStyleRes)

  private lateinit var binding: LayoutRicePoolHomeBinding

  private var countDownTimer: CountDownTimer? = null

  private var expireAt: String? = null

  var onExpiredFinish: () -> Unit = {}

  private fun init(context: Context, attrs: AttributeSet?) {
    binding = LayoutRicePoolHomeBinding.inflate(
      LayoutInflater.from(context),
      this, true
    )
  }

  fun onDestroy() {
    destroy()
  }

  fun setMessage(message: String?) {
    binding.tvPoolMessage.text = " ⋅ $message"
    binding.tvPoolMessage.isVisible = (message != null)
  }

  private fun destroy() {
    countDownTimer?.cancel()
    countDownTimer = null
  }

  fun setExpireAt(date: String?) {
    expireAt = date ?: return
    val expireTimeInMillis = toDateLocal(expireAt)
    if (expireTimeInMillis != null) {
      val now = Calendar.getInstance()
      val millis = expireTimeInMillis - now.timeInMillis
      setExpireAt(millis)
    }
  }

  private fun toDateLocal(utc: String?): Long? {
    val origin = utc ?: return null
    val dateFormatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    dateFormatter.timeZone = TimeZone.getTimeZone("UTC")
    try {
      val utcDate = dateFormatter.parse(origin)
      dateFormatter.timeZone = TimeZone.getDefault()
      val localTime = dateFormatter.format(utcDate)
      return dateFormatter.parse(localTime).time
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return null
  }

  private fun setExpireAt(millis: Long) {
    val isExpired = (millis <= 0)
    changeExpired(isExpired)
    if (isExpired) return

    destroy()
    countDownTimer = object : CountDownTimer(millis, 1000) {
      override fun onTick(millisUntilFinished: Long) {
        val minute = getMinute(millisUntilFinished)
        val seconds = getSeconds(millisUntilFinished)
        val text = "${minute}m : ${seconds}s"
        binding.tvPoolTimer.text = text
        binding.root.minWidth = ceil(
          (text.length * DisplayUtil.dp2Px(8f))
            .plus(ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_42dp))
        ).toInt()
//                Log.i("thom", "${context.toString()} rice pool $seconds")
      }

      override fun onFinish() {
        destroy()
        changeExpired(true)
        onExpiredFinish()
      }
    }
    countDownTimer?.start()
  }

  private fun getMinute(ms: Long): String {
    var minute = ms.div(1000).div(60)
    return if (minute < 10) {
      if (minute < 0) {
        minute = 0
      }
      "0$minute"
    } else {
      minute.toString()
    }
  }

  private fun getSeconds(ms: Long): String {
    val minute = ms.div(1000).div(60)
    val minutesMills = minute.times(1000).times(60)
    var seconds = ceil((ms - minutesMills).div(1000f)).toLong() % 60
    return if (seconds < 10) {
      if (seconds < 0) {
        seconds = 0
      }
      "0$seconds"
    } else {
      seconds.toString()
    }
  }

  private fun changeExpired(isExpired: Boolean) {
    if (isExpired) {
      binding.ivPoolTimer.setImageResource(com.ricepo.style.R.drawable.ic_timer_expired)
      binding.tvPoolTimer.text = ResourcesUtil.getString(com.ricepo.style.R.string.restaurant_pool_expired)
      binding.tvPoolTimer.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.gray7, binding.root))
      binding.tvPoolMessage.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.gray7, binding.root))
    } else {
      binding.ivPoolTimer.setImageResource(com.ricepo.style.R.drawable.ic_timer)
      binding.tvPoolTimer.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.ricePoolText, binding.root))
      binding.tvPoolMessage.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.ricePoolText, binding.root))
    }
  }
}
