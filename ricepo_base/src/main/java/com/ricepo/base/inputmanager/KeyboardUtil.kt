package com.ricepo.base.inputmanager

import android.content.Context
import android.os.Handler
import android.os.IBinder
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText

//
// Created by <PERSON><PERSON> on 25/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object KeyboardUtil {

  /**
   * hide the soft keyboard
   */
  fun hideKeyboard(context: Context, view: View) {
//        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
//        val imm = context.getSystemService(InputMethodManager::class.java)
//        if (view != null && imm != null) {
//            imm.hideSoftInputFromWindow(view.windowToken, 0)
//        }
    hideKeyboard(context, view.windowToken)
  }

  /**
   * hide the soft keyboard by window token
   */
  fun hideKeyboard(context: Context, windowToken: IBinder?) {
//        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    val imm = context.getSystemService(InputMethodManager::class.java)
    if (windowToken != null && imm != null) {
      imm.hideSoftInputFromWindow(windowToken, 0)
    }
  }

  /**
   * show the soft keyboard
   */
  fun showKeyboard(context: Context, view: View) {
//        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    val imm = context.getSystemService(InputMethodManager::class.java)

    if (view != null && imm != null) {
      view.requestFocus()
      // flags 0 or SHOW_IMPLICIT SHOW_FORCED
      Handler().postDelayed(
        {
          imm.showSoftInput(view, 0)
        },
        100
      )
    }
  }

  /**
   * toggle keyboard
   */
  fun toggleKeyboard(view: View) {
    if (view == null) return
//        val imm = view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    val imm = view.context.getSystemService(InputMethodManager::class.java)

    view.requestFocus()
    imm?.toggleSoftInput(0, InputMethodManager.HIDE_IMPLICIT_ONLY)
  }

  /**
   * return the boolean with should hide keyboard
   */
  fun isShouldHideKeyboard(
    v: View?,
    event: MotionEvent
  ): Boolean {
    if (v != null && v is EditText) {
      val l = intArrayOf(0, 0)
      v.getLocationInWindow(l)
      val left = l[0]
      val top = l[1]
      val bottom = top + v.getHeight()
      val right = left + v.getWidth()
      return !(event.x > left && event.x < right && event.y > top && event.y < bottom)
    }
    return false
  }
}
