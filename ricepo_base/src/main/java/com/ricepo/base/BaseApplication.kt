package com.ricepo.base

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.util.Log
import com.ricepo.base.remoteconfig.RiceRemoteConfig
import com.ricepo.base.remoteconfig.base.RemoteKonfig
import com.ricepo.base.tools.SystemUtils
import com.ricepo.monitor.log.Logger
import com.ricepo.style.ResourceApplication
import com.ricepo.tripartite.google.AdIdClient
import com.ricepo.tripartite.google.DeviceIdUtils
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import com.ricepo.base.BuildConfig

//
// Created by <PERSON><PERSON> on 23/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class BaseApplication : ResourceApplication() {

  companion object {
    lateinit var context: Context

    lateinit var mDeviceId: String
  }

  override fun onCreate() {
    super.onCreate()
    context = applicationContext

    // Logger init
    Logger.init(context)

//        deviceId = uuid()

    GlobalScope.launch {
      mDeviceId = adid()
      afterInitDeviceId(mDeviceId)

      Log.i("thom", "hexDeviceId: ${DeviceIdUtils.getDeviceId(context)}")
      Logger.i("thom", "deviceId = $mDeviceId")
    }
    RemoteKonfig.initialize {
      minimumFetchIntervalInSeconds = if (BuildConfig.DEBUG) 0L else 3600L
      registerModels(RiceRemoteConfig)
    }
  }

  protected open fun afterInitDeviceId(id: String?) {
  }

  /**
   * check and save uuid from content resolver
   */
  private fun uuid(): String {
    var content = SystemUtils.checkUUIDInner(this)
    if (content.isNullOrEmpty()) {
      content = SystemUtils.createUUIDInner(this)
    }
    return content
  }

  private suspend fun adid(): String {
    return AdIdClient.checkUUIDPref(this)
      ?: AdIdClient.determineAdvertisingInfo(this)
  }
}
