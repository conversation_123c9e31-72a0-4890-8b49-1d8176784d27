package com.ricepo.base.animation

import android.content.Context
import android.graphics.Bitmap
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.ricepo.base.BaseApplication
import com.ricepo.base.R
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.DrawableAnimationView

//
// Created by <PERSON><PERSON> on 5/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class LoadingView : FrameLayout {

  constructor(context: Context) : super(context) {
    init(context, null)
  }

  constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
    init(context, attrs)
  }

  constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(context, attrs)
    }

  private var loadingView: DrawableAnimationView? = null

  private fun init(context: Context?, attrs: AttributeSet?) {
    // application context not use app theme
    val view = LayoutInflater.from(context ?: BaseApplication.context)
      .inflate(R.layout.frame_loading, this)
    loadingView = view.findViewById(R.id.iv_loading)
    loadingView?.setData(getLoadingData())
    loadingView?.isRepeat = true
    loadingView?.mAnimTime = 16
    loadingView?.setGravity(Gravity.FILL)
    loadingView?.setAnimCallBack(object : DrawableAnimationView.AnimCallBack {
      override fun onAnimChange(position: Int, bitmap: Bitmap?) {
      }

      override fun onAnimEnd() {
      }
    })
  }

  private fun getLoadingData(): ArrayList<DrawableAnimationView.AnimData>? {
    val datas: ArrayList<DrawableAnimationView.AnimData> = ArrayList()
    var data: DrawableAnimationView.AnimData
    var resId: Int
    var fileName = "loading"
    var suffixValue = ""
    for (i in 0..29) {
      if (i < 10) {
        suffixValue += "0"
      }
      resId = ResourcesUtil.getDrawableId("${fileName}_00$suffixValue$i")
      data = DrawableAnimationView.AnimData()
      data.filePath = resId
      datas.add(data)
      suffixValue = ""
    }
    return datas
  }

  fun showLoading() {
    // use the glide to load gif
//        ImageLoader.load(loadingView, R.drawable.loading, true)
    loadingView?.start()
  }

  fun destroy() {
//        ImageLoader.with(loadingView)?.onDestroy()
    loadingView?.quit()
    loadingView = null
  }

  fun clear() {
//        ImageLoader.with(loadingView)?.clear(loadingView)
    loadingView?.quit()
  }
}
