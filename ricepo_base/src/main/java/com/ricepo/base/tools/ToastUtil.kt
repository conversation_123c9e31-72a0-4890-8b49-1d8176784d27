package com.ricepo.base.tools

import android.content.Context
import android.os.Build
import android.view.Gravity
import android.widget.Toast
import com.ricepo.base.BaseApplication
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 30/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
object ToastUtil {

  var toast: Toast? = null

  /**
   * show toast message in center
   */
  fun showToast(message: String?) {
    if (message?.isNullOrEmpty() == true) {
      return
    }
    if (Build.VERSION.SDK_INT == Build.VERSION_CODES.P || toast == null) {
      toast = Toast.makeText(BaseApplication.context, message, Toast.LENGTH_SHORT)
    } else {
      toast?.setText(message)
    }
//        val toast = Toast.makeText(BaseApplication.context, message, Toast.LENGTH_SHORT)
    toast?.setGravity(Gravity.CENTER, 0, 0)
    toast?.show()
  }

  /**
   * show toast message in center
   */
  fun showToast(resId: Int) {
    val message = ResourcesUtil.getString(resId)
    showToast(message)
  }

  /**
   * show long toast message in
   */
  private fun showLongToast(context: Context, message: String?) {
  }

  fun cancel() {
    toast?.cancel()
  }
}
