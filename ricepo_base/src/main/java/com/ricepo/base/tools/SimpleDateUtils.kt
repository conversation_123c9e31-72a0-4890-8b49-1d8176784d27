package com.ricepo.base.tools

import java.lang.Exception
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.TimeZone

//
// Created by <PERSON><PERSON> on 12/21/20.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
object SimpleDateUtils {

  fun toDateDiff(utc: String?): Long {
    val expireTimeInMillis = toDateLocal(utc)
    return if (expireTimeInMillis != null) {
      val now = Calendar.getInstance()
      val millis = expireTimeInMillis - now.timeInMillis
      millis
    } else {
      -1
    }
  }

  fun toDateLocal(utc: String?): Long? {
    val origin = utc ?: return null
    val dateFormatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    dateFormatter.timeZone = TimeZone.getTimeZone("UTC")
    try {
      val utcDate = dateFormatter.parse(origin)
      dateFormatter.timeZone = TimeZone.getDefault()
      val localTime = dateFormatter.format(utcDate)
      return dateFormatter.parse(localTime).time
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return null
  }

  /**
   * return seconds from now to utc
   */
  fun toDateDiffSeconds(utc: String?): Long {
    var ms = toDateDiff(utc)
    if (ms < 0) ms = 0
    return ms.div(1000)
  }
}
